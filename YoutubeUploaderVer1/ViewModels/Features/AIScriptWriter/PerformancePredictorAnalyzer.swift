//
//  PerformancePredictorAnalyzer.swift
//  YoutubeUploaderVer1
//
//  Created by AI Assistant on 08/06/25.
//

import Foundation
import SwiftUI

// MARK: - Performance Data Models

struct PerformancePrediction: Identifiable, Codable {
    let id = UUID()
    let overallScore: Double // 0-100
    let category: PerformanceCategory
    let metrics: PerformanceMetrics
    let factors: PerformanceFactors
    let recommendations: [String]
    let confidence: Double // 0-100
    let analysisDate: Date
}

struct PerformanceMetrics: Codable {
    let estimatedViews: ViewRange
    let engagementRate: Double // 0-100
    let retentionRate: Double // 0-100
    let clickThroughRate: Double // 0-100
    let viralPotential: Double // 0-100
    let algorithmScore: Double // 0-100
}

struct ViewRange: Codable {
    let minimum: Int
    let maximum: Int
    let mostLikely: Int
    
    var displayRange: String {
        if minimum >= 1000000 {
            return "\(minimum/1000000)M - \(maximum/1000000)M views"
        } else if minimum >= 1000 {
            return "\(minimum/1000)K - \(maximum/1000)K views"
        } else {
            return "\(minimum) - \(maximum) views"
        }
    }
}

struct PerformanceFactors: Codable {
    let titleStrength: Double // 0-100
    let descriptionQuality: Double // 0-100
    let topicTrending: Double // 0-100
    let competitionLevel: Double // 0-100
    let uploadTiming: Double // 0-100
    let thumbnailPotential: Double // 0-100
    let audienceMatch: Double // 0-100
}

enum PerformanceCategory: String, CaseIterable, Codable {
    case viral = "Viral Potential"
    case highPerforming = "High Performing"
    case goodPerforming = "Good Performing"
    case average = "Average Performance"
    case belowAverage = "Below Average"
    case poorPerforming = "Poor Performing"
    
    var color: Color {
        switch self {
        case .viral: return .purple
        case .highPerforming: return .green
        case .goodPerforming: return .blue
        case .average: return .orange
        case .belowAverage: return .yellow
        case .poorPerforming: return .red
        }
    }
    
    var icon: String {
        switch self {
        case .viral: return "flame.fill"
        case .highPerforming: return "arrow.up.circle.fill"
        case .goodPerforming: return "checkmark.circle.fill"
        case .average: return "minus.circle.fill"
        case .belowAverage: return "arrow.down.circle.fill"
        case .poorPerforming: return "xmark.circle.fill"
        }
    }
    
    var description: String {
        switch self {
        case .viral: return "Exceptional viral potential with massive reach"
        case .highPerforming: return "Strong performance with excellent engagement"
        case .goodPerforming: return "Solid performance with good audience response"
        case .average: return "Typical performance for this content type"
        case .belowAverage: return "May struggle to gain traction"
        case .poorPerforming: return "Likely to underperform significantly"
        }
    }
}

// MARK: - Performance Predictor Analyzer

@MainActor
class PerformancePredictorAnalyzer: ObservableObject {
    @Published var isAnalyzing = false
    @Published var analysisProgress = 0.0
    @Published var currentStep = ""
    @Published var prediction: PerformancePrediction?
    @Published var errorMessage: String?
    
    private let localAIService = LocalAIService.shared
    
    // MARK: - Public Methods
    
    func predictPerformance(
        title: String,
        description: String,
        category: String,
        thumbnailDescription: String = "",
        targetAudience: String = "General",
        uploadTime: String = "Peak Hours"
    ) async {
        isAnalyzing = true
        errorMessage = nil
        prediction = nil
        analysisProgress = 0.0
        
        // Step 1: Analyze content factors
        await updateProgress(0.2, "Analyzing content factors...")
        let contentAnalysis = await analyzeContentFactors(
            title: title,
            description: description,
            category: category,
            thumbnailDescription: thumbnailDescription
        )
        
        // Step 2: Analyze market factors
        await updateProgress(0.4, "Analyzing market conditions...")
        let marketAnalysis = await analyzeMarketFactors(
            title: title,
            category: category,
            uploadTime: uploadTime
        )
        
        // Step 3: Predict performance metrics
        await updateProgress(0.6, "Predicting performance metrics...")
        let metricsAnalysis = await predictMetrics(
            contentAnalysis: contentAnalysis,
            marketAnalysis: marketAnalysis,
            targetAudience: targetAudience
        )
        
        // Step 4: Generate recommendations
        await updateProgress(0.8, "Generating optimization recommendations...")
        let recommendations = await generateRecommendations(
            contentAnalysis: contentAnalysis,
            marketAnalysis: marketAnalysis,
            metrics: metricsAnalysis
        )
        
        // Step 5: Compile final prediction
        await updateProgress(1.0, "Compiling prediction results...")
        
        let performanceFactors = compilePerformanceFactors(
            contentAnalysis: contentAnalysis,
            marketAnalysis: marketAnalysis
        )
        
        let overallScore = calculateOverallScore(factors: performanceFactors, metrics: metricsAnalysis)
        let category = determinePerformanceCategory(score: overallScore)
        let confidence = calculateConfidence(factors: performanceFactors)
        
        prediction = PerformancePrediction(
            overallScore: overallScore,
            category: category,
            metrics: metricsAnalysis,
            factors: performanceFactors,
            recommendations: recommendations,
            confidence: confidence,
            analysisDate: Date()
        )
        
        isAnalyzing = false
    }
    
    // MARK: - Private Methods
    
    private func updateProgress(_ progress: Double, _ step: String) async {
        analysisProgress = progress
        currentStep = step
        try? await Task.sleep(nanoseconds: 500_000_000) // 0.5 second delay for UX
    }
    
    private func analyzeContentFactors(
        title: String,
        description: String,
        category: String,
        thumbnailDescription: String
    ) async -> ContentAnalysis {
        let prompt = createContentAnalysisPrompt(
            title: title,
            description: description,
            category: category,
            thumbnailDescription: thumbnailDescription
        )
        
        await localAIService.send(prompt: prompt)
        let response = localAIService.response
        
        return parseContentAnalysis(response: response)
    }
    
    private func analyzeMarketFactors(
        title: String,
        category: String,
        uploadTime: String
    ) async -> MarketAnalysis {
        let prompt = createMarketAnalysisPrompt(
            title: title,
            category: category,
            uploadTime: uploadTime
        )
        
        await localAIService.send(prompt: prompt)
        let response = localAIService.response
        
        return parseMarketAnalysis(response: response)
    }
    
    private func predictMetrics(
        contentAnalysis: ContentAnalysis,
        marketAnalysis: MarketAnalysis,
        targetAudience: String
    ) async -> PerformanceMetrics {
        let prompt = createMetricsPredictionPrompt(
            contentAnalysis: contentAnalysis,
            marketAnalysis: marketAnalysis,
            targetAudience: targetAudience
        )
        
        await localAIService.send(prompt: prompt)
        let response = localAIService.response
        
        return parseMetricsPrediction(response: response)
    }
    
    private func generateRecommendations(
        contentAnalysis: ContentAnalysis,
        marketAnalysis: MarketAnalysis,
        metrics: PerformanceMetrics
    ) async -> [String] {
        let prompt = createRecommendationsPrompt(
            contentAnalysis: contentAnalysis,
            marketAnalysis: marketAnalysis,
            metrics: metrics
        )
        
        await localAIService.send(prompt: prompt)
        let response = localAIService.response
        
        return parseRecommendations(response: response)
    }
}

// MARK: - Supporting Types

struct ContentAnalysis {
    let titleStrength: Double
    let descriptionQuality: Double
    let thumbnailPotential: Double
    let topicRelevance: Double
    let uniqueness: Double
}

struct MarketAnalysis {
    let competitionLevel: Double
    let trendingScore: Double
    let timingScore: Double
    let seasonality: Double
    let audienceInterest: Double
}

// MARK: - Helper Extensions

extension PerformancePredictorAnalyzer {
    private func compilePerformanceFactors(
        contentAnalysis: ContentAnalysis,
        marketAnalysis: MarketAnalysis
    ) -> PerformanceFactors {
        return PerformanceFactors(
            titleStrength: contentAnalysis.titleStrength,
            descriptionQuality: contentAnalysis.descriptionQuality,
            topicTrending: marketAnalysis.trendingScore,
            competitionLevel: 100 - marketAnalysis.competitionLevel, // Invert competition (lower is better)
            uploadTiming: marketAnalysis.timingScore,
            thumbnailPotential: contentAnalysis.thumbnailPotential,
            audienceMatch: marketAnalysis.audienceInterest
        )
    }
    
    private func calculateOverallScore(factors: PerformanceFactors, metrics: PerformanceMetrics) -> Double {
        let factorScore = (
            factors.titleStrength * 0.2 +
            factors.descriptionQuality * 0.15 +
            factors.topicTrending * 0.2 +
            factors.competitionLevel * 0.15 +
            factors.uploadTiming * 0.1 +
            factors.thumbnailPotential * 0.1 +
            factors.audienceMatch * 0.1
        )
        
        let metricsScore = (
            metrics.engagementRate * 0.3 +
            metrics.retentionRate * 0.25 +
            metrics.clickThroughRate * 0.2 +
            metrics.viralPotential * 0.15 +
            metrics.algorithmScore * 0.1
        )
        
        return (factorScore + metricsScore) / 2
    }
    
    private func determinePerformanceCategory(score: Double) -> PerformanceCategory {
        switch score {
        case 90...100: return .viral
        case 75..<90: return .highPerforming
        case 60..<75: return .goodPerforming
        case 45..<60: return .average
        case 30..<45: return .belowAverage
        default: return .poorPerforming
        }
    }
    
    private func calculateConfidence(factors: PerformanceFactors) -> Double {
        // Higher confidence when factors are more consistent
        let factorValues = [
            factors.titleStrength,
            factors.descriptionQuality,
            factors.topicTrending,
            factors.competitionLevel,
            factors.uploadTiming,
            factors.thumbnailPotential,
            factors.audienceMatch
        ]
        
        let average = factorValues.reduce(0, +) / Double(factorValues.count)
        let variance = factorValues.map { pow($0 - average, 2) }.reduce(0, +) / Double(factorValues.count)
        let standardDeviation = sqrt(variance)
        
        // Lower standard deviation = higher confidence
        return max(50, 100 - (standardDeviation * 2))
    }

    // MARK: - AI Prompt Generation

    private func createContentAnalysisPrompt(
        title: String,
        description: String,
        category: String,
        thumbnailDescription: String
    ) -> String {
        return """
        Analyze the content quality and appeal of this YouTube video for performance prediction.

        **Video Details:**
        Title: \(title)
        Description: \(description)
        Category: \(category)
        Thumbnail Description: \(thumbnailDescription.isEmpty ? "Not provided" : thumbnailDescription)

        **Analysis Required:**
        Evaluate each factor on a scale of 0-100:

        1. **Title Strength**: How compelling, clickable, and SEO-optimized is the title?
        2. **Description Quality**: How well does the description engage and inform?
        3. **Thumbnail Potential**: Based on description, how clickable would the thumbnail be?
        4. **Topic Relevance**: How relevant and interesting is this topic currently?
        5. **Content Uniqueness**: How unique is this content compared to existing videos?

        **Format your response as:**
        TITLE_STRENGTH: [0-100 score]
        TITLE_REASONING: [Brief explanation]
        DESCRIPTION_QUALITY: [0-100 score]
        DESCRIPTION_REASONING: [Brief explanation]
        THUMBNAIL_POTENTIAL: [0-100 score]
        THUMBNAIL_REASONING: [Brief explanation]
        TOPIC_RELEVANCE: [0-100 score]
        TOPIC_REASONING: [Brief explanation]
        UNIQUENESS: [0-100 score]
        UNIQUENESS_REASONING: [Brief explanation]
        """
    }

    private func createMarketAnalysisPrompt(
        title: String,
        category: String,
        uploadTime: String
    ) -> String {
        return """
        Analyze the market conditions and competition for this YouTube video.

        **Video Details:**
        Title: \(title)
        Category: \(category)
        Upload Time: \(uploadTime)

        **Market Analysis Required:**
        Evaluate each factor on a scale of 0-100:

        1. **Competition Level**: How saturated is this topic/niche? (0=no competition, 100=extremely saturated)
        2. **Trending Score**: How trending/popular is this topic currently?
        3. **Timing Score**: How optimal is the upload timing for this content?
        4. **Seasonality**: How well does this content align with current season/trends?
        5. **Audience Interest**: How much interest would the target audience have?

        **Format your response as:**
        COMPETITION_LEVEL: [0-100 score]
        COMPETITION_REASONING: [Brief explanation]
        TRENDING_SCORE: [0-100 score]
        TRENDING_REASONING: [Brief explanation]
        TIMING_SCORE: [0-100 score]
        TIMING_REASONING: [Brief explanation]
        SEASONALITY: [0-100 score]
        SEASONALITY_REASONING: [Brief explanation]
        AUDIENCE_INTEREST: [0-100 score]
        AUDIENCE_REASONING: [Brief explanation]
        """
    }

    private func createMetricsPredictionPrompt(
        contentAnalysis: ContentAnalysis,
        marketAnalysis: MarketAnalysis,
        targetAudience: String
    ) -> String {
        return """
        Predict specific performance metrics for this YouTube video based on content and market analysis.

        **Content Analysis Results:**
        - Title Strength: \(Int(contentAnalysis.titleStrength))/100
        - Description Quality: \(Int(contentAnalysis.descriptionQuality))/100
        - Thumbnail Potential: \(Int(contentAnalysis.thumbnailPotential))/100
        - Topic Relevance: \(Int(contentAnalysis.topicRelevance))/100
        - Uniqueness: \(Int(contentAnalysis.uniqueness))/100

        **Market Analysis Results:**
        - Competition Level: \(Int(marketAnalysis.competitionLevel))/100
        - Trending Score: \(Int(marketAnalysis.trendingScore))/100
        - Timing Score: \(Int(marketAnalysis.timingScore))/100
        - Seasonality: \(Int(marketAnalysis.seasonality))/100
        - Audience Interest: \(Int(marketAnalysis.audienceInterest))/100

        **Target Audience:** \(targetAudience)

        **Predict Performance Metrics:**
        Based on the analysis above, predict realistic performance metrics:

        **Format your response as:**
        VIEW_RANGE_MIN: [minimum expected views as number]
        VIEW_RANGE_MAX: [maximum expected views as number]
        VIEW_RANGE_LIKELY: [most likely view count as number]
        ENGAGEMENT_RATE: [0-100 expected engagement rate]
        RETENTION_RATE: [0-100 expected retention rate]
        CLICK_THROUGH_RATE: [0-100 expected CTR]
        VIRAL_POTENTIAL: [0-100 viral potential score]
        ALGORITHM_SCORE: [0-100 YouTube algorithm favorability]
        PREDICTION_REASONING: [Brief explanation of the predictions]
        """
    }

    private func createRecommendationsPrompt(
        contentAnalysis: ContentAnalysis,
        marketAnalysis: MarketAnalysis,
        metrics: PerformanceMetrics
    ) -> String {
        return """
        Generate specific, actionable recommendations to improve video performance.

        **Current Performance Prediction:**
        - Estimated Views: \(metrics.estimatedViews.displayRange)
        - Engagement Rate: \(Int(metrics.engagementRate))%
        - Retention Rate: \(Int(metrics.retentionRate))%
        - Viral Potential: \(Int(metrics.viralPotential))/100

        **Weakest Areas:**
        - Title Strength: \(Int(contentAnalysis.titleStrength))/100
        - Description Quality: \(Int(contentAnalysis.descriptionQuality))/100
        - Competition Level: \(Int(marketAnalysis.competitionLevel))/100 (high = more competition)

        **Generate 5-7 specific, actionable recommendations to improve performance:**
        Focus on the weakest areas and provide concrete steps the creator can take.

        **Format your response as:**
        RECOMMENDATION_1: [Specific actionable advice]
        RECOMMENDATION_2: [Specific actionable advice]
        RECOMMENDATION_3: [Specific actionable advice]
        RECOMMENDATION_4: [Specific actionable advice]
        RECOMMENDATION_5: [Specific actionable advice]
        RECOMMENDATION_6: [Specific actionable advice]
        RECOMMENDATION_7: [Specific actionable advice]
        """
    }

    // MARK: - Response Parsing

    private func parseContentAnalysis(response: String) -> ContentAnalysis {
        let titleStrength = extractScore(from: response, key: "TITLE_STRENGTH") ?? 50.0
        let descriptionQuality = extractScore(from: response, key: "DESCRIPTION_QUALITY") ?? 50.0
        let thumbnailPotential = extractScore(from: response, key: "THUMBNAIL_POTENTIAL") ?? 50.0
        let topicRelevance = extractScore(from: response, key: "TOPIC_RELEVANCE") ?? 50.0
        let uniqueness = extractScore(from: response, key: "UNIQUENESS") ?? 50.0

        return ContentAnalysis(
            titleStrength: titleStrength,
            descriptionQuality: descriptionQuality,
            thumbnailPotential: thumbnailPotential,
            topicRelevance: topicRelevance,
            uniqueness: uniqueness
        )
    }

    private func parseMarketAnalysis(response: String) -> MarketAnalysis {
        let competitionLevel = extractScore(from: response, key: "COMPETITION_LEVEL") ?? 50.0
        let trendingScore = extractScore(from: response, key: "TRENDING_SCORE") ?? 50.0
        let timingScore = extractScore(from: response, key: "TIMING_SCORE") ?? 50.0
        let seasonality = extractScore(from: response, key: "SEASONALITY") ?? 50.0
        let audienceInterest = extractScore(from: response, key: "AUDIENCE_INTEREST") ?? 50.0

        return MarketAnalysis(
            competitionLevel: competitionLevel,
            trendingScore: trendingScore,
            timingScore: timingScore,
            seasonality: seasonality,
            audienceInterest: audienceInterest
        )
    }

    private func parseMetricsPrediction(response: String) -> PerformanceMetrics {
        let viewMin = extractNumber(from: response, key: "VIEW_RANGE_MIN") ?? 100
        let viewMax = extractNumber(from: response, key: "VIEW_RANGE_MAX") ?? 1000
        let viewLikely = extractNumber(from: response, key: "VIEW_RANGE_LIKELY") ?? 500

        let engagementRate = extractScore(from: response, key: "ENGAGEMENT_RATE") ?? 3.0
        let retentionRate = extractScore(from: response, key: "RETENTION_RATE") ?? 45.0
        let clickThroughRate = extractScore(from: response, key: "CLICK_THROUGH_RATE") ?? 5.0
        let viralPotential = extractScore(from: response, key: "VIRAL_POTENTIAL") ?? 20.0
        let algorithmScore = extractScore(from: response, key: "ALGORITHM_SCORE") ?? 50.0

        return PerformanceMetrics(
            estimatedViews: ViewRange(minimum: viewMin, maximum: viewMax, mostLikely: viewLikely),
            engagementRate: engagementRate,
            retentionRate: retentionRate,
            clickThroughRate: clickThroughRate,
            viralPotential: viralPotential,
            algorithmScore: algorithmScore
        )
    }

    private func parseRecommendations(response: String) -> [String] {
        var recommendations: [String] = []

        for i in 1...7 {
            if let recommendation = extractText(from: response, key: "RECOMMENDATION_\(i)") {
                recommendations.append(recommendation)
            }
        }

        return recommendations.isEmpty ? ["Optimize your title for better click-through rates", "Improve thumbnail design for higher visibility"] : recommendations
    }

    private func extractScore(from text: String, key: String) -> Double? {
        let pattern = "\(key):\\s*(\\d+(?:\\.\\d+)?)"
        let regex = try? NSRegularExpression(pattern: pattern, options: .caseInsensitive)
        let range = NSRange(text.startIndex..<text.endIndex, in: text)

        if let match = regex?.firstMatch(in: text, options: [], range: range),
           let scoreRange = Range(match.range(at: 1), in: text) {
            return Double(text[scoreRange])
        }

        return nil
    }

    private func extractNumber(from text: String, key: String) -> Int? {
        let pattern = "\(key):\\s*(\\d+)"
        let regex = try? NSRegularExpression(pattern: pattern, options: .caseInsensitive)
        let range = NSRange(text.startIndex..<text.endIndex, in: text)

        if let match = regex?.firstMatch(in: text, options: [], range: range),
           let numberRange = Range(match.range(at: 1), in: text) {
            return Int(text[numberRange])
        }

        return nil
    }

    private func extractText(from text: String, key: String) -> String? {
        let pattern = "\(key):\\s*(.+?)(?=\\n[A-Z_]+:|$)"
        let regex = try? NSRegularExpression(pattern: pattern, options: [.caseInsensitive, .dotMatchesLineSeparators])
        let range = NSRange(text.startIndex..<text.endIndex, in: text)

        if let match = regex?.firstMatch(in: text, options: [], range: range),
           let textRange = Range(match.range(at: 1), in: text) {
            return String(text[textRange]).trimmingCharacters(in: .whitespacesAndNewlines)
        }

        return nil
    }
}
