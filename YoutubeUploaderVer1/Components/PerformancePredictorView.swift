//
//  PerformancePredictorView.swift
//  YoutubeUploaderVer1
//
//  Created by AI Assistant on 08/06/25.
//

import SwiftUI
#if os(macOS)
import AppKit
#else
import UIKit
#endif

struct PerformancePredictorView: View {
    let title: String
    let description: String
    let category: String
    let thumbnailDescription: String
    
    @StateObject private var analyzer = PerformancePredictorAnalyzer()
    @State private var showCopyAlert = false
    @State private var copiedText = ""
    
    var body: some View {
        VStack(alignment: .leading, spacing: 24) {
            // Modern Header
            modernHeaderSection

            // Analysis Progress
            if analyzer.isAnalyzing {
                modernAnalysisProgressSection
            }

            // Results Grid
            if let prediction = analyzer.prediction, !analyzer.isAnalyzing {
                modernResultsSection(prediction: prediction)
            }

            // Error Section
            if let error = analyzer.errorMessage, !analyzer.isAnalyzing {
                modernErrorSection(error: error)
            }

            // Predict Button
            if !analyzer.isAnalyzing {
                modernPredictButtonSection
            }
        }
        .padding(24)
        .background(AppColor.darkGrayBackground.color.opacity(0.5))
        .cornerRadius(16)
        .overlay(
            RoundedRectangle(cornerRadius: 16)
                .stroke(AppColor.grayText.color.opacity(0.1), lineWidth: 1)
        )
        .alert("Copied!", isPresented: $showCopyAlert) {
            Button("OK") { }
        } message: {
            Text("Performance data copied to clipboard successfully!")
        }
    }
    
    // MARK: - Modern Header Section
    private var modernHeaderSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack(spacing: 12) {
                Image(systemName: "chart.line.uptrend.xyaxis")
                    .font(.system(size: 24, weight: .semibold))
                    .foregroundColor(AppColor.youtubeRed.color)

                VStack(alignment: .leading, spacing: 4) {
                    Text("Performance Insights")
                        .font(.system(size: 18, weight: .bold))
                        .foregroundColor(AppColor.primary.color)

                    Text("Predict how your video will perform")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(AppColor.grayText.color)
                }

                Spacer()
            }

            // Quick Info Cards
            HStack(spacing: 12) {
                quickInfoCard(
                    icon: "eye.fill",
                    title: "Views",
                    subtitle: "Estimated reach"
                )

                quickInfoCard(
                    icon: "heart.fill",
                    title: "Engagement",
                    subtitle: "Audience interaction"
                )

                quickInfoCard(
                    icon: "star.fill",
                    title: "Success Rate",
                    subtitle: "Performance score"
                )
            }
        }
    }

    private func quickInfoCard(icon: String, title: String, subtitle: String) -> some View {
        HStack(spacing: 8) {
            Image(systemName: icon)
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(AppColor.youtubeRed.color)
                .frame(width: 20)

            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.system(size: 12, weight: .semibold))
                    .foregroundColor(AppColor.primary.color)

                Text(subtitle)
                    .font(.system(size: 10, weight: .medium))
                    .foregroundColor(AppColor.grayText.color)
            }

            Spacer()
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 8)
        .background(AppColor.darkGrayBackground.color.opacity(0.3))
        .cornerRadius(8)
    }
    
    // MARK: - Modern Predict Button Section
    private var modernPredictButtonSection: some View {
        VStack(spacing: 12) {
            // Info message when requirements not met
            if title.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty || description.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
                HStack(spacing: 8) {
                    Image(systemName: "info.circle.fill")
                        .font(.system(size: 16))
                        .foregroundColor(.blue)

                    Text("Please fill out the video title and description to enable performance prediction")
                        .font(AppFontStyle.caption1.style)
                        .foregroundColor(AppColor.grayText.color)
                        .fixedSize(horizontal: false, vertical: true)

                    Spacer()
                }
                .padding(.horizontal, 16)
                .padding(.vertical, 12)
                .background(Color.blue.opacity(0.1))
                .cornerRadius(8)
                .overlay(
                    RoundedRectangle(cornerRadius: 8)
                        .stroke(Color.blue.opacity(0.3), lineWidth: 1)
                )
            }

            Button(action: {
                Task {
                    await analyzer.predictPerformance(
                        title: title,
                        description: description,
                        category: category,
                        thumbnailDescription: thumbnailDescription
                    )
                }
            }) {
                HStack(spacing: 12) {
                    Image(systemName: "chart.line.uptrend.xyaxis")
                        .font(.system(size: 18))

                    Text("Predict Performance")
                        .font(AppFontStyle.headline.style.weight(.semibold))
                }
                .padding(.horizontal, 32)
                .padding(.vertical, 16)
                .frame(maxWidth: .infinity)
                .background(isButtonEnabled ? AppColor.youtubeRed.color : AppColor.grayText.color.opacity(0.3))
                .foregroundColor(isButtonEnabled ? .white : AppColor.grayText.color)
                .cornerRadius(12)
                .shadow(color: isButtonEnabled ? AppColor.youtubeRed.color.opacity(0.4) : Color.clear, radius: isButtonEnabled ? 8 : 0, x: 0, y: isButtonEnabled ? 4 : 0)
            }
            .buttonStyle(.plain)
            .disabled(!isButtonEnabled)
        }
    }

    private var isButtonEnabled: Bool {
        !title.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty &&
        !description.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty
    }
    

    
    // MARK: - Modern Analysis Progress Section
    private var modernAnalysisProgressSection: some View {
        VStack(spacing: 16) {
            HStack {
                ProgressView()
                    .scaleEffect(0.8)
                    .progressViewStyle(CircularProgressViewStyle(tint: AppColor.youtubeRed.color))

                VStack(alignment: .leading, spacing: 4) {
                    Text("Analyzing Performance...")
                        .font(AppFontStyle.headline.style.weight(.semibold))
                        .foregroundColor(AppColor.primary.color)

                    Text(analyzer.currentStep)
                        .font(AppFontStyle.subheadline.style)
                        .foregroundColor(AppColor.grayText.color)
                }

                Spacer()
            }

            ProgressView(value: analyzer.analysisProgress)
                .progressViewStyle(LinearProgressViewStyle(tint: AppColor.youtubeRed.color))
                .scaleEffect(y: 2)
        }
        .padding(20)
        .background(AppColor.darkGrayBackground.color)
        .cornerRadius(12)
    }
    
    // MARK: - Modern Results Section
    private func modernResultsSection(prediction: PerformancePrediction) -> some View {
        VStack(alignment: .leading, spacing: 20) {
            // Overall Performance Score
            modernPerformanceOverview(prediction: prediction)

            // Key Metrics Grid
            modernMetricsGrid(metrics: prediction.metrics)

            // Content Quality Insights
            modernContentInsights(factors: prediction.factors)

            // Success Recommendations
            modernRecommendationsGrid(recommendations: prediction.recommendations)
        }
    }
    
    // MARK: - Modern Performance Overview
    private func modernPerformanceOverview(prediction: PerformancePrediction) -> some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Performance Score")
                .font(.system(size: 18, weight: .bold))
                .foregroundColor(AppColor.primary.color)

            // Centered Score Display
            VStack(spacing: 16) {
                // Score Circle
                ZStack {
                    Circle()
                        .stroke(AppColor.grayText.color.opacity(0.2), lineWidth: 12)
                        .frame(width: 120, height: 120)

                    Circle()
                        .trim(from: 0, to: prediction.overallScore / 100)
                        .stroke(prediction.category.color, lineWidth: 12)
                        .frame(width: 120, height: 120)
                        .rotationEffect(.degrees(-90))
                        .animation(.easeInOut(duration: 1.0), value: prediction.overallScore)

                    VStack(spacing: 4) {
                        Text("\(Int(prediction.overallScore))")
                            .font(.system(size: 32, weight: .bold))
                            .foregroundColor(AppColor.primary.color)

                        Text("out of 100")
                            .font(.system(size: 12, weight: .medium))
                            .foregroundColor(AppColor.grayText.color)
                    }
                }

                // Performance Category
                VStack(spacing: 8) {
                    HStack(spacing: 8) {
                        Image(systemName: prediction.category.icon)
                            .font(.system(size: 16, weight: .semibold))
                            .foregroundColor(prediction.category.color)

                        Text(getPerformanceCategoryText(prediction.category))
                            .font(.system(size: 16, weight: .semibold))
                            .foregroundColor(prediction.category.color)
                    }

                    Text("Confidence: \(Int(prediction.confidence))%")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(AppColor.grayText.color)
                }
            }
            .frame(maxWidth: .infinity)
        }
        .padding(20)
        .background(AppColor.darkGrayBackground.color.opacity(0.3))
        .cornerRadius(16)
    }

    private func getPerformanceCategoryText(_ category: PerformanceCategory) -> String {
        switch category {
        case .viral: return "Viral Potential Expected"
        case .highPerforming: return "High Performance Expected"
        case .goodPerforming: return "Good Performance Expected"
        case .average: return "Average Performance Expected"
        case .belowAverage: return "Below Average Performance"
        case .poorPerforming: return "Needs Improvement"
        }
    }
    
    // MARK: - Modern Metrics Grid
    private func modernMetricsGrid(metrics: PerformanceMetrics) -> some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Key Metrics")
                .font(.system(size: 18, weight: .bold))
                .foregroundColor(AppColor.primary.color)

            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 16) {
                modernMetricCard(
                    title: "Expected Views",
                    value: metrics.estimatedViews.displayRange,
                    icon: "eye.fill",
                    color: .blue,
                    description: "Estimated reach"
                )

                modernMetricCard(
                    title: "Audience Engagement",
                    value: "\(String(format: "%.1f", metrics.engagementRate))%",
                    icon: "heart.fill",
                    color: .red,
                    description: "Likes & comments"
                )

                modernMetricCard(
                    title: "Watch Time",
                    value: "\(String(format: "%.1f", metrics.retentionRate))%",
                    icon: "clock.fill",
                    color: .green,
                    description: "Viewer retention"
                )

                modernMetricCard(
                    title: "Click Rate",
                    value: "\(String(format: "%.1f", metrics.clickThroughRate))%",
                    icon: "hand.tap.fill",
                    color: .orange,
                    description: "Thumbnail clicks"
                )
            }
        }
    }

    private func modernMetricCard(title: String, value: String, icon: String, color: Color, description: String) -> some View {
        VStack(spacing: 12) {
            // Icon and Value
            VStack(spacing: 8) {
                Image(systemName: icon)
                    .font(.system(size: 24, weight: .semibold))
                    .foregroundColor(color)

                Text(value)
                    .font(.system(size: 18, weight: .bold))
                    .foregroundColor(AppColor.primary.color)
            }

            // Title and Description
            VStack(spacing: 4) {
                Text(title)
                    .font(.system(size: 14, weight: .semibold))
                    .foregroundColor(AppColor.primary.color)
                    .multilineTextAlignment(.center)

                Text(description)
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(AppColor.grayText.color)
                    .multilineTextAlignment(.center)
            }
        }
        .frame(maxWidth: .infinity)
        .padding(16)
        .background(AppColor.darkGrayBackground.color.opacity(0.3))
        .cornerRadius(12)
    }
    
    // MARK: - Modern Content Insights
    private func modernContentInsights(factors: PerformanceFactors) -> some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Content Quality")
                .font(.system(size: 18, weight: .bold))
                .foregroundColor(AppColor.primary.color)

            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 16) {
                contentInsightCard(
                    title: "Title Quality",
                    score: factors.titleStrength,
                    icon: "textformat",
                    color: .blue
                )

                contentInsightCard(
                    title: "Description",
                    score: factors.descriptionQuality,
                    icon: "doc.text",
                    color: .green
                )

                contentInsightCard(
                    title: "Topic Trend",
                    score: factors.topicTrending,
                    icon: "chart.line.uptrend.xyaxis",
                    color: .purple
                )

                contentInsightCard(
                    title: "Market Opportunity",
                    score: 100 - factors.competitionLevel,
                    icon: "target",
                    color: .orange
                )
            }
        }
    }

    private func contentInsightCard(title: String, score: Double, icon: String, color: Color) -> some View {
        VStack(spacing: 12) {
            // Icon and Score
            VStack(spacing: 8) {
                Image(systemName: icon)
                    .font(.system(size: 20, weight: .semibold))
                    .foregroundColor(color)

                // Score with visual indicator
                VStack(spacing: 4) {
                    Text("\(Int(score))")
                        .font(.system(size: 20, weight: .bold))
                        .foregroundColor(AppColor.primary.color)

                    // Quality indicator dots
                    HStack(spacing: 4) {
                        ForEach(0..<5, id: \.self) { index in
                            Circle()
                                .fill(index < Int(score / 20) ? color : AppColor.grayText.color.opacity(0.3))
                                .frame(width: 6, height: 6)
                        }
                    }
                }
            }

            // Title and Quality Text
            VStack(spacing: 4) {
                Text(title)
                    .font(.system(size: 14, weight: .semibold))
                    .foregroundColor(AppColor.primary.color)
                    .multilineTextAlignment(.center)

                Text(getQualityText(score: score))
                    .font(.system(size: 11, weight: .medium))
                    .foregroundColor(getQualityColor(score: score))
                    .multilineTextAlignment(.center)
            }
        }
        .frame(maxWidth: .infinity)
        .padding(16)
        .background(AppColor.darkGrayBackground.color.opacity(0.3))
        .cornerRadius(12)
    }

    private func getQualityText(score: Double) -> String {
        switch score {
        case 80...100: return "Excellent"
        case 60..<80: return "Good"
        case 40..<60: return "Average"
        case 20..<40: return "Needs Work"
        default: return "Poor"
        }
    }

    private func getQualityColor(score: Double) -> Color {
        switch score {
        case 80...100: return .green
        case 60..<80: return .blue
        case 40..<60: return .orange
        default: return .red
        }
    }
    
    // MARK: - Modern Recommendations Grid
    private func modernRecommendationsGrid(recommendations: [String]) -> some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text("Success Tips")
                    .font(.system(size: 18, weight: .bold))
                    .foregroundColor(AppColor.primary.color)

                Spacer()

                Button(action: {
                    copyRecommendations(recommendations)
                }) {
                    HStack(spacing: 6) {
                        Image(systemName: "doc.on.doc")
                            .font(.system(size: 12, weight: .medium))

                        Text("Copy All")
                            .font(.system(size: 12, weight: .medium))
                    }
                    .foregroundColor(AppColor.youtubeRed.color)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 6)
                    .background(AppColor.youtubeRed.color.opacity(0.1))
                    .cornerRadius(8)
                }
                .buttonStyle(.plain)
            }

            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 12) {
                ForEach(Array(recommendations.prefix(6).enumerated()), id: \.offset) { index, recommendation in
                    recommendationCard(
                        recommendation: recommendation,
                        index: index + 1
                    )
                }
            }
        }
    }

    private func recommendationCard(recommendation: String, index: Int) -> some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack(spacing: 8) {
                Text("\(index)")
                    .font(.system(size: 14, weight: .bold))
                    .foregroundColor(.white)
                    .frame(width: 24, height: 24)
                    .background(AppColor.youtubeRed.color)
                    .clipShape(Circle())

                Spacer()

                Image(systemName: "lightbulb.fill")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.yellow)
            }

            Text(recommendation)
                .font(.system(size: 13, weight: .medium))
                .foregroundColor(AppColor.primary.color)
                .fixedSize(horizontal: false, vertical: true)
                .lineLimit(4)
        }
        .padding(16)
        .background(AppColor.darkGrayBackground.color.opacity(0.3))
        .cornerRadius(12)
    }

    // MARK: - Commented Out Old Recommendations Section
//        VStack(alignment: .leading, spacing: 12) {
//            HStack {
//                Text("Optimization Recommendations")
//                    .font(AppFontStyle.headline.style.weight(.bold))
//                    .foregroundColor(AppColor.primary.color)
//                
//                Spacer()
//                
//                Button(action: {
//                    copyRecommendations(recommendations)
//                }) {
//                    HStack(spacing: 4) {
//                        Image(systemName: "doc.on.doc")
//                            .font(.system(size: 12))
//                        
//                        Text("Copy All")
//                            .font(AppFontStyle.caption1.style.weight(.medium))
//                    }
//                    .foregroundColor(AppColor.youtubeRed.color)
//                    .padding(.horizontal, 8)
//                    .padding(.vertical, 4)
//                    .background(AppColor.youtubeRed.color.opacity(0.1))
//                    .cornerRadius(6)
//                }
//            }
//            
//            VStack(alignment: .leading, spacing: 8) {
//                ForEach(recommendations.prefix(6), id: \.self) { recommendation in
//                    HStack(alignment: .top, spacing: 8) {
//                        Image(systemName: "lightbulb.fill")
//                            .font(.system(size: 12))
//                            .foregroundColor(.yellow)
//                            .padding(.top, 2)
//                        
//                        Text(recommendation)
//                            .font(AppFontStyle.caption1.style)
//                            .foregroundColor(AppColor.grayText.color)
//                            .fixedSize(horizontal: false, vertical: true)
//                    }
//                }
//            }
//        }
//        .padding(16)
//        .background(AppColor.darkGrayBackground.color.opacity(0.2))
//        .cornerRadius(12)
//    }
    
    // MARK: - Modern Error Section
    private func modernErrorSection(error: String) -> some View {
        VStack(spacing: 16) {
            HStack(spacing: 12) {
                Image(systemName: "exclamationmark.triangle.fill")
                    .font(.system(size: 24))
                    .foregroundColor(.orange)

                VStack(alignment: .leading, spacing: 4) {
                    Text("Prediction Failed")
                        .font(AppFontStyle.headline.style.weight(.semibold))
                        .foregroundColor(AppColor.primary.color)

                    Text(error)
                        .font(AppFontStyle.subheadline.style)
                        .foregroundColor(AppColor.grayText.color)
                }

                Spacer()
            }
        }
        .padding(20)
        .background(AppColor.darkGrayBackground.color.opacity(0.3))
        .cornerRadius(12)
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(Color.orange.opacity(0.3), lineWidth: 1)
        )
    }
    

    
    // MARK: - Helper Methods
    private func copyRecommendations(_ recommendations: [String]) {
        let text = recommendations.enumerated().map { index, recommendation in
            "\(index + 1). \(recommendation)"
        }.joined(separator: "\n\n")
        
        #if os(macOS)
        NSPasteboard.general.setString(text, forType: .string)
        #else
        UIPasteboard.general.string = text
        #endif
        
        copiedText = text
        showCopyAlert = true
    }
}
