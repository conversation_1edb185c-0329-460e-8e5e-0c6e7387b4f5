//
//  VideoItemView.swift
//  YoutubeUploaderVer1
//
//  Created by <PERSON><PERSON><PERSON><PERSON> B on 09/04/25.
//

import SwiftUI
import Foundation

//struct VideoItemView: View {
//    @EnvironmentObject var router:NavigationCoordinator
//
//    let video: YouTubeVideo
//    let isVideos:Bool
//    
//    var screenWidth: CGFloat {
//        NSScreen.main?.frame.width ?? 1440
//    }
//
//    var screenHeight: CGFloat {
//        NSScreen.main?.frame.height ?? 900
//    }
//
//    var body: some View {
//        HStack(spacing: 16) {
//            // Thumbnail
//            VStack {
//                VideoWebView(videoID: video.videoId)
//                    .aspectRatio(16/9, contentMode: .fill)
//                    .frame(width: screenWidth * 0.3)
//                    .shadow(radius: 4)
//            }
//            .clipShape(
//                RoundedRectangle(cornerRadius: 12)
//            )
//            
//            // Video details
//            VStack(alignment: .leading, spacing: 8) {
//                HStack {
//                    VStack(alignment: .leading, spacing: 2) {
//                        Text(video.title)
//                            .font(AppFontStyle.title2.style.bold())
//                            .foregroundColor(AppColor.primary.color)
//                            .lineLimit(2)
//                        
//                        Text("Published on \(formattedTimeAgo(from: video.publishedAt))")
//                            .font(AppFontStyle.callout.style)
//                            .foregroundColor(AppColor.grayText.color)
//                    }
//                    HStack {
//                        Spacer()
//                        
//                        CustomButton(text: "View Analytics") {
//                            if isVideos {
//                                router.navigateToViewAnalytics(video: video)
//                            } else {
//                                router.navigateToPlaylistVideoAnalyticsView(video: video)
//                            }
//                        }
//
//                        
//                    }
//                }
//                // Stats Grid 1
//                LazyVGrid(columns: Array(repeating: GridItem(.flexible(), spacing: 16), count: 4), spacing: 16) {
//                    statItem(icon: "eye.fill", color: .blue, label: "Views", value: formatNumber(video.analytics?.views ?? 0))
//                    statItem(icon: "hand.thumbsup.fill", color: .green, label: "Likes", value: formatNumber(video.analytics?.likes ?? 0))
//                    statItem(icon: "text.bubble.fill", color: .orange, label: "Comments", value: formatNumber(video.analytics?.comments ?? 0))
//                    statItem(icon: "square.and.arrow.up.fill", color: .purple, label: "Shares", value: formatNumber(video.analytics?.shares ?? 0))
//                }
//
//                // Stats Grid 2
//                LazyVGrid(columns: Array(repeating: GridItem(.flexible(), spacing: 16), count: 4), spacing: 16) {
//                    statItem(icon: "person.2.fill", color: .teal, label: "Engagement", value: "\(String(format: "%.1f", video.analytics?.engagementRate ?? 0))%")
//                    statItem(icon: "clock.fill", color: .pink, label: "Avg Watch", value: "\(video.analytics?.averageViewDuration ?? 0) sec")
//                    statItem(icon: "percent", color: .yellow, label: "Retention", value: "\(Int(video.analytics?.retentionRate ?? 0))%")
//                    subscriberStatsItem(
//                        gained: video.analytics?.subscribersGained ?? 0,
//                        lost: video.analytics?.subscribersLost ?? 0
//                    )
//
//                }
//                
//            }
//            .padding()
//            .frame(maxWidth: .infinity)
//            .background(AppColor.darkBackground.color)
//            .clipShape(
//                RoundedRectangle(cornerRadius: 20)
//            )
//        }
//        .padding(16)
//        .frame(height: screenHeight * 0.25)
//        .background(AppColor.darkBackground.color)
//        .clipShape(
//            RoundedRectangle(cornerRadius: 12)
//        )
//    }
//    
//    func subscriberStatsItem(gained: Int, lost: Int) -> some View {
//        let gainedText = gained > 0 ? formatNumber(gained) : "0"
//        let lostText = lost > 0 ? formatNumber(lost) : "0"
//
//        return VStack(spacing: 6) {
//            HStack(spacing: 6) {
//                Image(systemName: "chart.line.uptrend.xyaxis")
//                    .foregroundColor(.green)
//                Text(gainedText)
//                    .foregroundColor(AppColor.primary.color)
//                Image(systemName: "chart.line.downtrend.xyaxis")
//                    .foregroundColor(.red)
//                Text(lostText)
//                    .foregroundColor(AppColor.primary.color)
//            }
//            .font(AppFontStyle.title2.style)
//
//            Text("Subs Trend")
//                .foregroundColor(Color.grayTextColor)
//                .font(AppFontStyle.callout.style)
//        }
//        .frame(maxWidth: .infinity)
//        .padding(6)
//        .padding(.vertical,7)
//        .background(AppColor.youtubeRed.color.opacity(0.1))
//        .clipShape(
//            RoundedRectangle(cornerRadius: 12)
//        )
//    }
//
//
//    func statItem(icon: String, color: Color, label: String, value: String) -> some View {
//        VStack(spacing: 6) {
//            HStack(spacing: 6) {
//                Image(systemName: icon)
//                    .foregroundColor(color)
//                    .font(AppFontStyle.title2.style) // upgraded from .title
//
//                Text(value)
//                    .foregroundColor(AppColor.primary.color)
//                    .font(AppFontStyle.title2.style) // upgraded from .title
//            }
//            
//            Text(label)
//                .foregroundColor(AppColor.grayText.color)
//                .font(AppFontStyle.callout.style) // upgraded from .subheadline
//        }
//        .frame(maxWidth: .infinity)
//        .padding(6)
//        .padding(.vertical,7)
//        .background(AppColor.youtubeRed.color.opacity(0.1))
//        .clipShape(
//            RoundedRectangle(cornerRadius: 12)
//        )
//    }
//}
struct VideoItemView: View {
    @EnvironmentObject var router: NavigationCoordinator
    
    let video: YouTubeVideo
    let isVideos: Bool
    
    var screenWidth: CGFloat {
        NSScreen.main?.frame.width ?? 1440
    }
    
    var screenHeight: CGFloat {
        NSScreen.main?.frame.height ?? 900
    }
    
    var body: some View {
        VStack(spacing: 0) {
            // Card Container
            HStack(spacing: 0) {
                // Thumbnail Section
                thumbnailSection
                
                // Content Section
                contentSection
            }
        }
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(AppColor.darkBackground.color)
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 2)
        )
        .overlay(
            RoundedRectangle(cornerRadius: 16)
                .stroke(AppColor.grayText.color.opacity(0.1), lineWidth: 1)
        )
//        .padding(.horizontal, 16)
//        .padding(.vertical, 8)
    }
    
    // MARK: - Thumbnail Section
    private var thumbnailSection: some View {
        VStack {
            ZStack {
                // Video Thumbnail
                VideoWebView(videoID: video.videoId)
                    .aspectRatio(16/9, contentMode: .fill)
                    .clipShape(RoundedRectangle(cornerRadius: 12))
                
                
                // Status Badge (bottom right)
                VStack {
                    Spacer()
                    HStack {
                        Spacer()
                        statusBadge
                    }
                }
                .padding(8)
                
                // Publication Date Badge (bottom left)
                VStack {
                    Spacer()
                    HStack {
                        publicationBadge
                        Spacer()
                    }
                }
                .padding(8)
            }
        }
        .frame(width: screenWidth * 0.28)
        .padding(.leading, 20)
        .padding(.vertical, 20)
    }
    
    // MARK: - Content Section
    private var contentSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            // Header with title and analytics button
            headerSection
            
            // Metrics Grid
            metricsGrid
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 20)
        .frame(maxWidth: .infinity, alignment: .leading)
    }
    
    // MARK: - Header Section
    private var headerSection: some View {
        HStack(alignment: .top, spacing: 16) {
            VStack(alignment: .leading, spacing: 8) {
                Text(video.title)
                    .font(AppFontStyle.title2.style.bold())
                    .foregroundColor(AppColor.primary.color)
                    .lineLimit(2)
                    .multilineTextAlignment(.leading)
                
                HStack(spacing: 8) {
                    statusPill
                    
                    Text("•")
                        .foregroundColor(AppColor.grayText.color)
                        .font(AppFontStyle.caption1.style)
                    
                    Text("Published \(formattedTimeAgo(from: video.publishedAt))")
                        .font(AppFontStyle.caption1.style)
                        .foregroundColor(AppColor.grayText.color)
                }
            }
            
            Spacer()
            
            // Analytics Button
            analyticsButton
        }
    }
    
    // MARK: - Metrics Grid
    private var metricsGrid: some View {
        LazyVGrid(
            columns: Array(repeating: GridItem(.flexible(), spacing: 12), count: 4),
            spacing: 12
        ) {
            modernStatItem(
                icon: "eye.fill",
                color: .blue,
                bgColor: Color.blue.opacity(0.1),
                label: "Views",
                value: formatNumber(video.analytics?.views ?? 0)
            )
            
            modernStatItem(
                icon: "hand.thumbsup.fill",
                color: .green,
                bgColor: Color.green.opacity(0.1),
                label: "Likes",
                value: formatNumber(video.analytics?.likes ?? 0)
            )
            
            modernStatItem(
                icon: "text.bubble.fill",
                color: .orange,
                bgColor: Color.orange.opacity(0.1),
                label: "Comments",
                value: formatNumber(video.analytics?.comments ?? 0)
            )
            
            modernStatItem(
                icon: "square.and.arrow.up.fill",
                color: .purple,
                bgColor: Color.purple.opacity(0.1),
                label: "Shares",
                value: formatNumber(video.analytics?.shares ?? 0)
            )
            
            modernStatItem(
                icon: "person.2.fill",
                color: .cyan,
                bgColor: Color.cyan.opacity(0.1),
                label: "Engagement",
                value: "\(String(format: "%.1f", video.analytics?.engagementRate ?? 0))%"
            )
            
            modernStatItem(
                icon: "clock.fill",
                color: .pink,
                bgColor: Color.pink.opacity(0.1),
                label: "Avg Watch",
                value: "\(video.analytics?.averageViewDuration ?? 0) sec"
            )
            
            modernStatItem(
                icon: "percent",
                color: .yellow,
                bgColor: Color.yellow.opacity(0.1),
                label: "Retention",
                value: "\(Int(video.analytics?.retentionRate ?? 0))%"
            )
            
            modernSubscriberStatsItem(
                gained: video.analytics?.subscribersGained ?? 0,
                lost: video.analytics?.subscribersLost ?? 0
            )
        }
    }
    
    // MARK: - UI Components
    private var statusBadge: some View {
        Text("YouTube")
            .font(AppFontStyle.caption2.style.bold())
            .foregroundColor(.white)
            .padding(.horizontal, 8)
            .padding(.vertical, 4)
            .background(
                RoundedRectangle(cornerRadius: 6)
                    .fill(Color.black.opacity(0.8))
            )
    }
    
    private var publicationBadge: some View {
        HStack(spacing: 4) {
            Image(systemName: "clock.fill")
                .font(.system(size: 10))
            Text(formattedTimeAgo(from: video.publishedAt))
                .font(AppFontStyle.caption2.style)
        }
        .foregroundColor(.white)
        .padding(.horizontal, 8)
        .padding(.vertical, 4)
        .background(
            RoundedRectangle(cornerRadius: 6)
                .fill(Color.black.opacity(0.8))
        )
    }
    
    private var statusPill: some View {
        Text("Published")
            .font(AppFontStyle.caption2.style.bold())
            .foregroundColor(AppColor.grayText.color)
            .padding(.horizontal, 8)
            .padding(.vertical, 4)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(AppColor.grayText.color.opacity(0.3), lineWidth: 1)
            )
    }
    
    private var analyticsButton: some View {
        Button(action: {
            if isVideos {
                router.navigateToViewAnalytics(video: video)
            } else {
                router.navigateToPlaylistVideoAnalyticsView(video: video)
            }
        }) {
            HStack(spacing: 6) {
                Image(systemName: "chart.bar.fill")
                    .font(.system(size: 14, weight: .medium))
                Text("View Analytics")
                    .font(AppFontStyle.callout.style.bold())
            }
            .foregroundColor(.white)
            .padding(.horizontal, 16)
            .padding(.vertical, 10)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(AppColor.youtubeRed.color)
            )
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(1.0)
        .onHover { isHovered in
            withAnimation(.easeInOut(duration: 0.2)) {
                // Add hover effect if needed
            }
        }
    }
    
    // MARK: - Modern Stat Item
    func modernStatItem(icon: String, color: Color, bgColor: Color, label: String, value: String) -> some View {
        HStack(spacing: 8) {
            // Icon with background
            RoundedRectangle(cornerRadius: 8)
                .fill(bgColor)
                .frame(width: 32, height: 32)
                .overlay(
                    Image(systemName: icon)
                        .foregroundColor(color)
                        .font(.system(size: 14, weight: .semibold))
                )
            
            VStack(alignment: .leading, spacing: 2) {
                Text(value)
                    .font(AppFontStyle.title3.style.bold())
                    .foregroundColor(AppColor.primary.color)
                
                Text(label)
                    .font(AppFontStyle.caption2.style)
                    .foregroundColor(AppColor.grayText.color)
            }
            
            Spacer()
        }
        .padding(.horizontal, 10)
        .padding(.vertical, 10)
        .background(
            RoundedRectangle(cornerRadius: 10)
                .fill(AppColor.grayText.color.opacity(0.05))
        )
    }
    
    func modernSubscriberStatsItem(gained: Int, lost: Int) -> some View {
        let gainedText = gained > 0 ? formatNumber(gained) : "0"
        let lostText = lost > 0 ? formatNumber(lost) : "0"
        let netChange = gained - lost
        let trendColor: Color = netChange > 0 ? .green : netChange < 0 ? .red : .gray
        
        return HStack(spacing: 8) {
            // Icon with background
            RoundedRectangle(cornerRadius: 8)
                .fill(AppColor.accentGreen.color.opacity(0.1))
                .frame(width: 32, height: 32)
                .overlay(
                    Image(systemName: "chart.line.uptrend.xyaxis")
                        .foregroundColor(AppColor.accentGreen.color)
                        .font(.system(size: 14, weight: .semibold))
                )
            
            VStack(alignment: .leading, spacing: 2) {
                HStack(spacing: 4) {
                    Text(netChange > 0 ? "+\(netChange)" : "\(netChange)")
                        .font(AppFontStyle.title3.style.bold())
                        .foregroundColor(trendColor)
                    
                    Image(systemName: netChange > 0 ? "arrow.up" : netChange < 0 ? "arrow.down" : "minus")
                        .font(.system(size: 10, weight: .bold))
                        .foregroundColor(trendColor)
                }
                
                Text("Subs Trend")
                    .font(AppFontStyle.caption1.style)
                    .foregroundColor(AppColor.grayText.color)
            }
            
            Spacer()
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 10)
        .background(
            RoundedRectangle(cornerRadius: 10)
                .fill(AppColor.grayText.color.opacity(0.05))
        )
    }
}

// MARK: - Color Extensions
extension Color {
    static let emerald = Color(red: 0.2, green: 0.7, blue: 0.4)
    static let cyan = Color(red: 0.0, green: 0.7, blue: 0.8)
}
