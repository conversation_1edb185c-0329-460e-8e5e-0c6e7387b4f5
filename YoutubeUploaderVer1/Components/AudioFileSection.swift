//
//  AudioFileSection.swift
//  YoutubeUploaderVer1
//
//  Created by Shashank B on 05/05/25.
//

import SwiftUI
import AVFoundation
import UniformTypeIdentifiers

struct AudioFileSection: View {
    @Binding var isVisible: Bool
    let audioFileURL: URL?
    let transcriptText: [(TimeInterval, TimeInterval, String)]

    @State private var audioPlayer: AVAudioPlayer?
    @State private var isPlaying = false
    @State private var isShowingSavePanel = false
    @StateObject private var translationManager = AudioTranslationManager()

    var body: some View {
        if isVisible, let _ = audioFileURL {
            VStack(alignment: .leading, spacing: 16) {
                header
                HStack(spacing:15) {
                    audioWaveform
                    controls
                }
            }
            .padding(24)
            .background(
                RoundedRectangle(cornerRadius: 24)
                    .fill(AppColor.darkGrayBackground.color.opacity(0.5))
            )
            .overlay(
                RoundedRectangle(cornerRadius: 24)
                    .stroke(AppColor.grayText.color.opacity(0.1), lineWidth: 1)
            )
            .shadow(color: Color.black.opacity(0.1), radius: 8, x: 0, y: 4)
            .transition(.opacity.combined(with: .move(edge: .top)))
            .alert("Translation Error", isPresented: $translationManager.showErrorAlert) {
                Button("OK") {
                    translationManager.showErrorAlert = false
                }
            } message: {
                Text(translationManager.errorMessage ?? "An unknown error occurred")
            }
        }
    }

    private var header: some View {
        HStack {
            Text("Audio File")
                .font(AppFontStyle.headline.style.weight(.bold))
                .foregroundColor(AppColor.primary.color)

            Spacer()

            Button {
                isVisible = false
            } label: {
                Image(systemName: "xmark.circle.fill")
                    .font(.system(size: 20))
                    .foregroundColor(AppColor.grayText.color)
            }
            .buttonStyle(.plain)
        }
    }

    private var audioWaveform: some View {
        HStack(spacing: 20) {
            ZStack {
                RoundedRectangle(cornerRadius: 16)
                    .fill(AppColor.darkGrayBackground.color.opacity(0.5))

                HStack(spacing: 3) {
                    ForEach(0..<60, id: \.self) { _ in
                        RoundedRectangle(cornerRadius: 1)
                            .fill(AppColor.primary.color.opacity(0.7))
                            .frame(width: 3, height: CGFloat(10 + Int.random(in: 5...40)))
                    }
                }
                .padding(.horizontal)
                
                HStack {
                    Button(action: {
                        if let url = audioFileURL {
                            togglePlayback(url: url)
                        }
                    }) {
                        Image(systemName: isPlaying ? "pause.fill" : "play.fill")
                            .font(.system(size: 20, weight: .bold))
                            .foregroundColor(AppColor.primary.color)
                            .padding(8)
                            .background(Circle().fill(AppColor.darkBackground.color))
                            .shadow(radius: 3)
                    }
                    .padding(.leading, 12)
                    Spacer()
                }
                .buttonStyle(.plain)
            }
            .frame(height: 80)
        }
    }

    private var controls: some View {
        VStack(spacing: 12) {
            // Download Audio Button
            AudioControlButton(
                iconName: "arrow.down.circle",
                label: "Download Audio",
                backgroundColor: AppColor.youtubeRed.color,
                foregroundColor: .white,
                action: {
                    if let url = audioFileURL {
                        downloadAudio(from: url)
                    }
                }
            )
            .shadow(color: AppColor.youtubeRed.color.opacity(0.4), radius: 6, x: 0, y: 3)

            // Hindi Translation Section
            if !transcriptText.isEmpty {
                HindiAudioButton(
                    isTranslating: translationManager.isTranslating,
                    isPlaying: translationManager.isPlaying,
                    translationStep: translationManager.translationStep,
                    onTranslate: {
                        translationManager.translateAndSynthesizeAudio(
                            from: transcriptText,
                            to: .hindi
                        ) { audioURL in
                            // Audio is played directly during synthesis
                        }
                    },
                    onStop: {
                        translationManager.stopAudio()
                    }
                )
            }
        }
    }

    private func togglePlayback(url: URL) {
        do {
            if audioPlayer == nil {
                audioPlayer = try AVAudioPlayer(contentsOf: url)
            }

            if let player = audioPlayer {
                if player.isPlaying {
                    player.pause()
                    isPlaying = false
                } else {
                    player.play()
                    isPlaying = true
                }
            }
        } catch {
            print("Audio playback error: \(error.localizedDescription)")
        }
    }
    
    private func downloadAudio(from url: URL) {
        let savePanel = NSSavePanel()
        
        // Use UniformTypeIdentifiers (UTType) for allowed content types
        savePanel.allowedContentTypes = [
            UTType.mp3,
            UTType.wav,
        ]
        
        savePanel.nameFieldStringValue = url.lastPathComponent
        savePanel.canCreateDirectories = true
        
        if savePanel.runModal() == .OK, let destinationURL = savePanel.url {
            do {
                try FileManager.default.copyItem(at: url, to: destinationURL)
                print("File saved to \(destinationURL)")
            } catch {
                print("Download error: \(error.localizedDescription)")
            }
        }
    }
}

struct AudioControlButton: View {
    let iconName: String
    let label: String
    let backgroundColor: Color
    let foregroundColor: Color
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            HStack(spacing: 8) {
                Image(systemName: iconName)
                    .font(.system(size: 16))
                Text(label)
                    .font(AppFontStyle.body.style.weight(.semibold))
            }
            .padding(.horizontal, 20)
            .padding(.vertical, 12)
            .frame(height: 52)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(backgroundColor)
            )
            .foregroundColor(foregroundColor)
        }
        .buttonStyle(.plain)
    }
}



