//
//  ScriptWriterView.swift
//  YoutubeUploaderVer1
//
//  Created by AI Assistant on 05/01/25.
//

import SwiftUI
import MarkdownUI

struct ScriptWriterView: View {
    @StateObject private var scriptManager = ScriptWriterManager()
    
    let localAIService = LocalAIService.shared
    var body: some View {
        ScrollView {
            VStack(alignment: .leading, spacing: 24) {
                VStack(alignment: .leading, spacing: 8) {
                    Text(TextConstants.ScriptWriter.title)
                        .font(AppFontStyle.largeTitle.style.weight(.bold))
                        .foregroundColor(AppColor.primary.color)
                    
                    Text(TextConstants.ScriptWriter.subtitle)
                        .font(AppFontStyle.subheadline.style)
                        .foregroundColor(AppColor.grayText.color)
                }
                
                .padding(.horizontal, 4)
                // Script Parameters
                if !scriptManager.isGenerating {
                    scriptParametersSection
                }
                
                // Generation Progress
                if scriptManager.isGenerating {
                    generationProgressSection
                }
                
                // Generated Script Display
                if !scriptManager.generatedScript.isEmpty && !scriptManager.isGenerating {
                    generatedScriptSection
                }
                
                // Generate Button
                generateButtonSection
            }
            .padding(24)
        }
        .background(AppColor.darkBackground.color)
        .alert("Script Generation Error", isPresented: $scriptManager.showErrorAlert) {
            Button("OK") {
                scriptManager.showErrorAlert = false
            }
        } message: {
            Text(scriptManager.errorMessage ?? "An unknown error occurred")
        }
        .onAppear{
            Task{
                await localAIService.initializeModel()
            }
        }
    }
    
    
    private var scriptParametersSection: some View {
        VStack(alignment: .leading, spacing: 20) {
            // Topic Input
            VStack(alignment: .leading, spacing: 8) {
                Text("Video Topic *")
                    .font(AppFontStyle.headline.style.weight(.semibold))
                    .foregroundColor(AppColor.primary.color)
                
                TextField("Enter your video topic or idea...", text: $scriptManager.topic, axis: .vertical)
                    .textFieldStyle(.plain)
                    .padding(12)
                    .background(AppColor.darkGrayBackground.color)
                    .cornerRadius(8)
                    .overlay(
                        RoundedRectangle(cornerRadius: 8)
                            .stroke(AppColor.grayText.color.opacity(0.2), lineWidth: 1)
                    )
                    .lineLimit(3...6)
            }
            
            // Script Type Selection
            ScriptTypeSelector(selectedType: $scriptManager.selectedType)
            
            // Length and Tone
            HStack(spacing: 16) {
                VStack(alignment: .leading, spacing: 8) {
                    Text("Video Length")
                        .font(AppFontStyle.headline.style.weight(.semibold))
                        .foregroundColor(AppColor.primary.color)
                    
                    Menu {
                        ForEach(ScriptLength.allCases, id: \.self) { length in
                            Button(action: {
                                scriptManager.selectedLength = length
                            }) {
                                HStack {
                                    Text(length.rawValue)
                                    Spacer()
                                    if scriptManager.selectedLength == length {
                                        Image(systemName: "checkmark")
                                    }
                                }
                            }
                        }
                    } label: {
                        HStack {
                            Text(scriptManager.selectedLength.rawValue)
                                .foregroundColor(AppColor.primary.color)
                            Spacer()
                            Image(systemName: "chevron.down")
                                .foregroundColor(AppColor.grayText.color)
                        }
                        .padding(12)
                        .background(AppColor.darkGrayBackground.color)
                        .cornerRadius(8)
                        .overlay(
                            RoundedRectangle(cornerRadius: 8)
                                .stroke(AppColor.grayText.color.opacity(0.2), lineWidth: 1)
                        )
                    }
                    .buttonStyle(.plain)
                    
                    Text(scriptManager.selectedLength.estimatedWords)
                        .font(AppFontStyle.caption1.style)
                        .foregroundColor(AppColor.grayText.color)
                }
                
                VStack(alignment: .leading, spacing: 8) {
                    Text("Tone & Style")
                        .font(AppFontStyle.headline.style.weight(.semibold))
                        .foregroundColor(AppColor.primary.color)
                    
                    Menu {
                        ForEach(ScriptTone.allCases, id: \.self) { tone in
                            Button(action: {
                                scriptManager.selectedTone = tone
                            }) {
                                VStack(alignment: .leading) {
                                    HStack {
                                        Text(tone.rawValue)
                                        Spacer()
                                        if scriptManager.selectedTone == tone {
                                            Image(systemName: "checkmark")
                                        }
                                    }
                                    Text(tone.description)
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                }
                            }
                        }
                    } label: {
                        HStack {
                            Text(scriptManager.selectedTone.rawValue)
                                .foregroundColor(AppColor.primary.color)
                            Spacer()
                            Image(systemName: "chevron.down")
                                .foregroundColor(AppColor.grayText.color)
                        }
                        .padding(12)
                        .background(AppColor.darkGrayBackground.color)
                        .cornerRadius(8)
                        .overlay(
                            RoundedRectangle(cornerRadius: 8)
                                .stroke(AppColor.grayText.color.opacity(0.2), lineWidth: 1)
                        )
                    }
                    .buttonStyle(.plain)
                    
                    Text(scriptManager.selectedTone.description)
                        .font(AppFontStyle.caption1.style)
                        .foregroundColor(AppColor.grayText.color)
                }
            }
            
            // Optional Fields
            DisclosureGroup("Advanced Options") {
                VStack(alignment: .leading, spacing: 16) {
                    // Target Audience
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Target Audience")
                            .font(AppFontStyle.subheadline.style.weight(.medium))
                            .foregroundColor(AppColor.primary.color)
                        
                        TextField("e.g., Beginners, Tech enthusiasts, Young adults...", text: $scriptManager.targetAudience)
                            .textFieldStyle(.plain)
                            .padding(12)
                            .background(AppColor.darkGrayBackground.color)
                            .cornerRadius(8)
                            .overlay(
                                RoundedRectangle(cornerRadius: 8)
                                    .stroke(AppColor.grayText.color.opacity(0.2), lineWidth: 1)
                            )
                    }
                    
                    // Key Points
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Key Points to Cover")
                            .font(AppFontStyle.subheadline.style.weight(.medium))
                            .foregroundColor(AppColor.primary.color)
                        
                        TextField("List important points you want to include...", text: $scriptManager.keyPoints, axis: .vertical)
                            .textFieldStyle(.plain)
                            .padding(12)
                            .background(AppColor.darkGrayBackground.color)
                            .cornerRadius(8)
                            .overlay(
                                RoundedRectangle(cornerRadius: 8)
                                    .stroke(AppColor.grayText.color.opacity(0.2), lineWidth: 1)
                            )
                            .lineLimit(2...4)
                    }
                    
                    // Options
                    VStack(alignment: .leading, spacing: 12) {
                        Toggle("Include engaging hook", isOn: $scriptManager.includeHook)
                            .toggleStyle(SwitchToggleStyle(tint: AppColor.youtubeRed.color))
                        
                    }
                    .font(AppFontStyle.subheadline.style)
                    .foregroundColor(AppColor.primary.color)
                }
                .padding(.top, 12)
            }
            .font(AppFontStyle.subheadline.style.weight(.medium))
            .foregroundColor(AppColor.primary.color)
            .accentColor(AppColor.youtubeRed.color)
        }
        .padding(20)
        .background(AppColor.darkGrayBackground.color.opacity(0.5))
        .cornerRadius(16)
        .overlay(
            RoundedRectangle(cornerRadius: 16)
                .stroke(AppColor.grayText.color.opacity(0.1), lineWidth: 1)
        )
    }
    
    private var generationProgressSection: some View {
        VStack(spacing: 16) {
            HStack {
                ProgressView()
                    .scaleEffect(0.8)
                    .progressViewStyle(CircularProgressViewStyle(tint: AppColor.youtubeRed.color))
                
                VStack(alignment: .leading, spacing: 4) {
                    Text("Generating Script...")
                        .font(AppFontStyle.headline.style.weight(.semibold))
                        .foregroundColor(AppColor.primary.color)
                    
                    Text(scriptManager.currentStep)
                        .font(AppFontStyle.subheadline.style)
                        .foregroundColor(AppColor.grayText.color)
                }
                
                Spacer()
            }
            
            ProgressView(value: scriptManager.generationProgress)
                .progressViewStyle(LinearProgressViewStyle(tint: AppColor.youtubeRed.color))
                .scaleEffect(y: 2)
        }
        .padding(20)
        .background(AppColor.darkGrayBackground.color.opacity(0.5))
        .cornerRadius(16)
    }
    
    private var generatedScriptSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text("Generated Script")
                    .font(AppFontStyle.headline.style.weight(.bold))
                    .foregroundColor(AppColor.primary.color)
                
                Spacer()
                if !scriptManager.generatedScript.isEmpty {
                    HStack(spacing: 8) {
                        Button(action: {
                            scriptManager.copyScriptToClipboard()
                        }) {
                            HStack(spacing: 6) {
                              
                                    Image(systemName: "doc.on.clipboard")
                                        .font(.system(size: 14))
                                Text(scriptManager.isCopied ? "Copied !!" : "Copy")
                                        .font(AppFontStyle.caption1.style.weight(.semibold))
                                
                              
                            }
                            .padding(.horizontal, 12)
                            .padding(.vertical, 6)
                            .background(AppColor.grayText.color.opacity(0.2))
                            .foregroundColor(AppColor.primary.color)
                            .cornerRadius(8)
                        }
                        .buttonStyle(.plain)

                        Button(action: {
                            scriptManager.exportScript()
                        }) {
                            HStack(spacing: 6) {
                                Image(systemName: "square.and.arrow.up")
                                    .font(.system(size: 14))
                                Text("Export")
                                    .font(AppFontStyle.caption1.style.weight(.semibold))
                            }
                            .padding(.horizontal, 12)
                            .padding(.vertical, 6)
                            .background(AppColor.youtubeRed.color)
                            .foregroundColor(.white)
                            .cornerRadius(8)
                        }
                        .buttonStyle(.plain)
                    }
                }
                
                Button(action: {
                    scriptManager.reset()
                }) {
                    HStack(spacing: 6) {
                        Image(systemName: "arrow.clockwise")
                            .font(.system(size: 14))
                        Text("New Script")
                            .font(AppFontStyle.caption1.style.weight(.semibold))
                    }
                    .padding(.horizontal, 12)
                    .padding(.vertical, 6)
                    .background(AppColor.grayText.color.opacity(0.2))
                    .foregroundColor(AppColor.primary.color)
                    .cornerRadius(8)
                }
                .buttonStyle(.plain)
            }
            
            ScrollView {
                Markdown(scriptManager.generatedScript)
                    .font(AppFontStyle.body.style)
                    .foregroundColor(AppColor.primary.color)
                    .textSelection(.enabled)
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .padding(16)
            }
            .frame(minHeight: 300, maxHeight: 500)
            .background(AppColor.darkGrayBackground.color)
            .cornerRadius(12)
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(AppColor.grayText.color.opacity(0.2), lineWidth: 1)
            )
        }
        .padding(20)
        .background(AppColor.darkGrayBackground.color.opacity(0.5))
        .cornerRadius(16)
    }
    
    private var generateButtonSection: some View {
        Button(action: {
            scriptManager.generateScript()
        }) {
            HStack(spacing: 12) {
                if scriptManager.isGenerating {
                    ProgressView()
                        .scaleEffect(0.8)
                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                } else {
                    Image(systemName: "wand.and.stars")
                        .font(.system(size: 18))
                }
                
                Text(scriptManager.isGenerating ? "Generating Script..." : "Generate Script")
                    .font(AppFontStyle.headline.style.weight(.semibold))
            }
            .padding(.horizontal, 32)
            .padding(.vertical, 16)
            .frame(maxWidth: .infinity)
            .background(AppColor.youtubeRed.color)
            .foregroundColor(.white)
            .cornerRadius(12)
            .shadow(color: AppColor.youtubeRed.color.opacity(0.4), radius: 8, x: 0, y: 4)
        }
        .buttonStyle(.plain)
        .disabled(scriptManager.isGenerating || scriptManager.topic.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty)
        .opacity(scriptManager.isGenerating || scriptManager.topic.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty ? 0.6 : 1.0)
    }
}

struct ScriptTypeSelector: View {
    @Binding var selectedType: ScriptType

    let columns = [
        GridItem(.adaptive(minimum: 140), spacing: 12)
    ]

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Video Type")
                .font(AppFontStyle.headline.style.weight(.semibold))
                .foregroundColor(AppColor.primary.color)

            LazyVGrid(columns: columns, spacing: 12) {
                ForEach(ScriptType.allCases, id: \.self) { type in
                    Button(action: {
                        selectedType = type
                    }) {
                        VStack(spacing: 8) {
                            Image(systemName: type.icon)
                                .font(.system(size: 24))
                                .foregroundColor(selectedType == type ? .white : AppColor.youtubeRed.color)

                            Text(type.rawValue)
                                .font(AppFontStyle.caption1.style.weight(.semibold))
                                .foregroundColor(selectedType == type ? .white : AppColor.primary.color)
                                .multilineTextAlignment(.center)
                        }
                        .padding(12)
                        .frame(minHeight: 80)
                        .frame(maxWidth: .infinity)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(selectedType == type ? AppColor.youtubeRed.color : AppColor.darkGrayBackground.color)
                        )
                        .overlay(
                            RoundedRectangle(cornerRadius: 12)
                                .stroke(
                                    selectedType == type ? AppColor.youtubeRed.color : AppColor.grayText.color.opacity(0.2),
                                    lineWidth: selectedType == type ? 2 : 1
                                )
                        )
                    }
                    .buttonStyle(.plain)
                }
            }

            if selectedType != .educational {
                Text(selectedType.description)
                    .font(AppFontStyle.caption1.style)
                    .foregroundColor(AppColor.grayText.color)
                    .padding(.top, 4)
            }
        }
    }
}

#Preview {
    ScriptWriterView()
}
