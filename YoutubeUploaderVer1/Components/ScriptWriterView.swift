//
//  ScriptWriterView.swift
//  YoutubeUploaderVer1
//
//  Created by AI Assistant on 05/01/25.
//

import SwiftUI
import MarkdownUI

struct ScriptWriterView: View {
    @StateObject private var scriptManager = ScriptWriterManager()
    
    let localAIService = LocalAIService.shared
    var body: some View {
        ScrollView {
            VStack(alignment: .leading, spacing: 16) {
                // Modern Header
                modernHeaderSection

                // Script Parameters
                if !scriptManager.isGenerating {
                    modernScriptParametersSection
                }

                // Generation Progress
                if scriptManager.isGenerating {
                    modernGenerationProgressSection
                }

                // Generated Script Display
                if !scriptManager.generatedScript.isEmpty && !scriptManager.isGenerating {
                    modernGeneratedScriptSection
                }

                // Generate Button
                modernGenerateButtonSection
            }
            .padding(20)
        }
        .background(AppColor.darkBackground.color)
        .alert("Script Generation Error", isPresented: $scriptManager.showErrorAlert) {
            Button("OK") {
                scriptManager.showErrorAlert = false
            }
        } message: {
            Text(scriptManager.errorMessage ?? "An unknown error occurred")
        }
        .onAppear{
            Task{
                await localAIService.initializeModel()
            }
        }
    }
    

    // MARK: - Modern Header Section
    private var modernHeaderSection: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack(spacing: 12) {
                Image(systemName: "doc.text.fill")
                    .font(.system(size: 24, weight: .semibold))
                    .foregroundColor(AppColor.youtubeRed.color)

                VStack(alignment: .leading, spacing: 2) {
                    Text("AI Script Writer")
                        .font(.system(size: 18, weight: .bold))
                        .foregroundColor(AppColor.primary.color)

                    Text("Generate professional video scripts with AI")
                        .font(.system(size: 13, weight: .medium))
                        .foregroundColor(AppColor.grayText.color)
                }

                Spacer()
            }

            // Quick Info Cards
            HStack(spacing: 8) {
                quickInfoCard(
                    icon: "wand.and.stars",
                    title: "AI Powered",
                    subtitle: "Smart generation"
                )

                quickInfoCard(
                    icon: "clock.fill",
                    title: "Fast",
                    subtitle: "Ready in minutes"
                )

                quickInfoCard(
                    icon: "doc.richtext",
                    title: "Professional",
                    subtitle: "Industry standard"
                )
            }
        }
    }

    private func quickInfoCard(icon: String, title: String, subtitle: String) -> some View {
        HStack(spacing: 6) {
            Image(systemName: icon)
                .font(.system(size: 12, weight: .medium))
                .foregroundColor(AppColor.youtubeRed.color)
                .frame(width: 16)

            VStack(alignment: .leading, spacing: 1) {
                Text(title)
                    .font(.system(size: 11, weight: .semibold))
                    .foregroundColor(AppColor.primary.color)

                Text(subtitle)
                    .font(.system(size: 9, weight: .medium))
                    .foregroundColor(AppColor.grayText.color)
            }

            Spacer()
        }
        .padding(.horizontal, 8)
        .padding(.vertical, 6)
        .background(AppColor.darkGrayBackground.color.opacity(0.3))
        .cornerRadius(6)
    }

    // MARK: - Modern Script Parameters Section
    private var modernScriptParametersSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Script Configuration")
                .font(.system(size: 16, weight: .bold))
                .foregroundColor(AppColor.primary.color)

            VStack(alignment: .leading, spacing: 12) {
                // Topic Input
                VStack(alignment: .leading, spacing: 6) {
                    Text("Video Topic *")
                        .font(.system(size: 14, weight: .semibold))
                        .foregroundColor(AppColor.primary.color)

                    TextField("Enter your video topic or idea...", text: $scriptManager.topic, axis: .vertical)
                        .textFieldStyle(.plain)
                        .padding(12)
                        .background(AppColor.darkGrayBackground.color)
                        .cornerRadius(8)
                        .overlay(
                            RoundedRectangle(cornerRadius: 8)
                                .stroke(AppColor.grayText.color.opacity(0.2), lineWidth: 1)
                        )
                        .lineLimit(3...6)
                }

                // Script Configuration Grid
                modernConfigurationGrid

                // Advanced Options
                modernAdvancedOptionsSection
            }
        }
        .padding(16)
        .background(AppColor.darkGrayBackground.color.opacity(0.5))
        .cornerRadius(12)
    }

    // MARK: - Modern Configuration Grid
    private var modernConfigurationGrid: some View {
        VStack(alignment: .leading, spacing: 10) {
            Text("Script Settings")
                .font(.system(size: 14, weight: .semibold))
                .foregroundColor(AppColor.primary.color)

            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 10) {
                modernConfigCard(
                    title: "Type",
                    value: scriptManager.selectedType.rawValue,
                    icon: scriptManager.selectedType.icon,
                    color: .blue
                )

                modernConfigCard(
                    title: "Length",
                    value: scriptManager.selectedLength.rawValue,
                    icon: "clock.fill",
                    color: .green
                )

                modernConfigCard(
                    title: "Tone",
                    value: scriptManager.selectedTone.rawValue,
                    icon: "speaker.wave.2.fill",
                    color: .purple
                )
            }

            // Expanded selectors
            modernScriptTypeSelector
            modernScriptLengthSelector
            modernScriptToneSelector
        }
    }

    private func modernConfigCard(title: String, value: String, icon: String, color: Color) -> some View {
        VStack(spacing: 6) {
            Image(systemName: icon)
                .font(.system(size: 16, weight: .semibold))
                .foregroundColor(color)

            VStack(spacing: 2) {
                Text(title)
                    .font(.system(size: 11, weight: .semibold))
                    .foregroundColor(AppColor.primary.color)

                Text(value)
                    .font(.system(size: 10, weight: .medium))
                    .foregroundColor(AppColor.grayText.color)
                    .lineLimit(1)
            }
        }
        .frame(maxWidth: .infinity)
        .padding(8)
        .background(AppColor.darkGrayBackground.color.opacity(0.3))
        .cornerRadius(8)
    }

    // MARK: - Modern Selectors
    private var modernScriptTypeSelector: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("Video Type")
                .font(.system(size: 13, weight: .semibold))
                .foregroundColor(AppColor.primary.color)

            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible()),
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 8) {
                ForEach(ScriptType.allCases, id: \.self) { type in
                    Button(action: {
                        scriptManager.selectedType = type
                    }) {
                        VStack(spacing: 4) {
                            Image(systemName: type.icon)
                                .font(.system(size: 16, weight: .medium))
                                .foregroundColor(scriptManager.selectedType == type ? .white : AppColor.youtubeRed.color)

                            Text(type.rawValue)
                                .font(.system(size: 10, weight: .semibold))
                                .foregroundColor(scriptManager.selectedType == type ? .white : AppColor.primary.color)
                                .multilineTextAlignment(.center)
                                .lineLimit(2)
                        }
                        .padding(8)
                        .frame(minHeight: 60)
                        .frame(maxWidth: .infinity)
                        .background(
                            RoundedRectangle(cornerRadius: 8)
                                .fill(scriptManager.selectedType == type ? AppColor.youtubeRed.color : AppColor.darkGrayBackground.color)
                        )
                        .overlay(
                            RoundedRectangle(cornerRadius: 8)
                                .stroke(
                                    scriptManager.selectedType == type ? AppColor.youtubeRed.color : AppColor.grayText.color.opacity(0.2),
                                    lineWidth: scriptManager.selectedType == type ? 2 : 1
                                )
                        )
                    }
                    .buttonStyle(.plain)
                }
            }
        }
    }

    private var modernScriptLengthSelector: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("Video Length")
                .font(.system(size: 13, weight: .semibold))
                .foregroundColor(AppColor.primary.color)

            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 8) {
                ForEach(ScriptLength.allCases, id: \.self) { length in
                    Button(action: {
                        scriptManager.selectedLength = length
                    }) {
                        VStack(spacing: 4) {
                            Text(length.rawValue)
                                .font(.system(size: 12, weight: .semibold))
                                .foregroundColor(scriptManager.selectedLength == length ? .white : AppColor.primary.color)

                            Text(length.estimatedWords)
                                .font(.system(size: 10, weight: .medium))
                                .foregroundColor(scriptManager.selectedLength == length ? .white.opacity(0.8) : AppColor.grayText.color)
                        }
                        .padding(10)
                        .frame(maxWidth: .infinity)
                        .background(
                            RoundedRectangle(cornerRadius: 8)
                                .fill(scriptManager.selectedLength == length ? AppColor.youtubeRed.color : AppColor.darkGrayBackground.color)
                        )
                        .overlay(
                            RoundedRectangle(cornerRadius: 8)
                                .stroke(
                                    scriptManager.selectedLength == length ? AppColor.youtubeRed.color : AppColor.grayText.color.opacity(0.2),
                                    lineWidth: scriptManager.selectedLength == length ? 2 : 1
                                )
                        )
                    }
                    .buttonStyle(.plain)
                }
            }
        }
    }

    private var modernScriptToneSelector: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("Tone & Style")
                .font(.system(size: 13, weight: .semibold))
                .foregroundColor(AppColor.primary.color)

            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 8) {
                ForEach(ScriptTone.allCases, id: \.self) { tone in
                    Button(action: {
                        scriptManager.selectedTone = tone
                    }) {
                        VStack(spacing: 4) {
                            Text(tone.rawValue)
                                .font(.system(size: 11, weight: .semibold))
                                .foregroundColor(scriptManager.selectedTone == tone ? .white : AppColor.primary.color)
                                .multilineTextAlignment(.center)
                                .lineLimit(1)

                            Text(tone.description)
                                .font(.system(size: 9, weight: .medium))
                                .foregroundColor(scriptManager.selectedTone == tone ? .white.opacity(0.8) : AppColor.grayText.color)
                                .multilineTextAlignment(.center)
                                .lineLimit(2)
                        }
                        .padding(8)
                        .frame(minHeight: 50)
                        .frame(maxWidth: .infinity)
                        .background(
                            RoundedRectangle(cornerRadius: 8)
                                .fill(scriptManager.selectedTone == tone ? AppColor.youtubeRed.color : AppColor.darkGrayBackground.color)
                        )
                        .overlay(
                            RoundedRectangle(cornerRadius: 8)
                                .stroke(
                                    scriptManager.selectedTone == tone ? AppColor.youtubeRed.color : AppColor.grayText.color.opacity(0.2),
                                    lineWidth: scriptManager.selectedTone == tone ? 2 : 1
                                )
                        )
                    }
                    .buttonStyle(.plain)
                }
            }
        }
    }

    // MARK: - Modern Advanced Options
    private var modernAdvancedOptionsSection: some View {
        DisclosureGroup("Advanced Options") {
            VStack(alignment: .leading, spacing: 12) {
                // Target Audience
                VStack(alignment: .leading, spacing: 6) {
                    Text("Target Audience")
                        .font(.system(size: 13, weight: .semibold))
                        .foregroundColor(AppColor.primary.color)

                    TextField("e.g., Beginners, Tech enthusiasts, Young adults...", text: $scriptManager.targetAudience)
                        .textFieldStyle(.plain)
                        .padding(10)
                        .background(AppColor.darkGrayBackground.color)
                        .cornerRadius(8)
                        .overlay(
                            RoundedRectangle(cornerRadius: 8)
                                .stroke(AppColor.grayText.color.opacity(0.2), lineWidth: 1)
                        )
                }

                // Key Points
                VStack(alignment: .leading, spacing: 6) {
                    Text("Key Points to Cover")
                        .font(.system(size: 13, weight: .semibold))
                        .foregroundColor(AppColor.primary.color)

                    TextField("List important points you want to include...", text: $scriptManager.keyPoints, axis: .vertical)
                        .textFieldStyle(.plain)
                        .padding(10)
                        .background(AppColor.darkGrayBackground.color)
                        .cornerRadius(8)
                        .overlay(
                            RoundedRectangle(cornerRadius: 8)
                                .stroke(AppColor.grayText.color.opacity(0.2), lineWidth: 1)
                        )
                        .lineLimit(2...4)
                }

                // Options
                HStack {
                    Toggle("Include engaging hook", isOn: $scriptManager.includeHook)
                        .toggleStyle(SwitchToggleStyle(tint: AppColor.youtubeRed.color))
                        .font(.system(size: 13, weight: .medium))
                        .foregroundColor(AppColor.primary.color)

                    Spacer()
                }
            }
            .padding(.top, 8)
        }
        .font(.system(size: 13, weight: .semibold))
        .foregroundColor(AppColor.primary.color)
        .accentColor(AppColor.youtubeRed.color)
    }

    // MARK: - Modern Generation Progress Section
    private var modernGenerationProgressSection: some View {
        VStack(spacing: 12) {
            HStack(spacing: 12) {
                ProgressView()
                    .scaleEffect(0.8)
                    .progressViewStyle(CircularProgressViewStyle(tint: AppColor.youtubeRed.color))

                VStack(alignment: .leading, spacing: 2) {
                    Text("Generating Script...")
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(AppColor.primary.color)

                    Text(scriptManager.currentStep)
                        .font(.system(size: 13, weight: .medium))
                        .foregroundColor(AppColor.grayText.color)
                }

                Spacer()

                Text("\(Int(scriptManager.generationProgress * 100))%")
                    .font(.system(size: 14, weight: .bold))
                    .foregroundColor(AppColor.youtubeRed.color)
            }

            ProgressView(value: scriptManager.generationProgress)
                .progressViewStyle(LinearProgressViewStyle(tint: AppColor.youtubeRed.color))
                .scaleEffect(y: 1.5)
        }
        .padding(16)
        .background(AppColor.darkGrayBackground.color.opacity(0.5))
        .cornerRadius(12)
    }

    // MARK: - Modern Generated Script Section
    private var modernGeneratedScriptSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text("Generated Script")
                    .font(.system(size: 16, weight: .bold))
                    .foregroundColor(AppColor.primary.color)

                Spacer()

                HStack(spacing: 6) {
                    if !scriptManager.generatedScript.isEmpty {
                        Button(action: {
                            scriptManager.copyScriptToClipboard()
                        }) {
                            HStack(spacing: 4) {
                                Image(systemName: "doc.on.clipboard")
                                    .font(.system(size: 12))
                                Text(scriptManager.isCopied ? "Copied!" : "Copy")
                                    .font(.system(size: 11, weight: .semibold))
                            }
                            .padding(.horizontal, 8)
                            .padding(.vertical, 4)
                            .background(AppColor.grayText.color.opacity(0.2))
                            .foregroundColor(AppColor.primary.color)
                            .cornerRadius(6)
                        }
                        .buttonStyle(.plain)

                        Button(action: {
                            scriptManager.exportScript()
                        }) {
                            HStack(spacing: 4) {
                                Image(systemName: "square.and.arrow.up")
                                    .font(.system(size: 12))
                                Text("Export")
                                    .font(.system(size: 11, weight: .semibold))
                            }
                            .padding(.horizontal, 8)
                            .padding(.vertical, 4)
                            .background(AppColor.youtubeRed.color)
                            .foregroundColor(.white)
                            .cornerRadius(6)
                        }
                        .buttonStyle(.plain)
                    }

                    Button(action: {
                        scriptManager.reset()
                    }) {
                        HStack(spacing: 4) {
                            Image(systemName: "arrow.clockwise")
                                .font(.system(size: 12))
                            Text("New")
                                .font(.system(size: 11, weight: .semibold))
                        }
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(AppColor.grayText.color.opacity(0.2))
                        .foregroundColor(AppColor.primary.color)
                        .cornerRadius(6)
                    }
                    .buttonStyle(.plain)
                }
            }

            ScrollView {
                Markdown(scriptManager.generatedScript)
                    .font(.system(size: 14, weight: .regular))
                    .foregroundColor(AppColor.primary.color)
                    .textSelection(.enabled)
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .padding(12)
            }
            .frame(minHeight: 300, maxHeight: 500)
            .background(AppColor.darkGrayBackground.color)
            .cornerRadius(10)
            .overlay(
                RoundedRectangle(cornerRadius: 10)
                    .stroke(AppColor.grayText.color.opacity(0.2), lineWidth: 1)
            )
        }
        .padding(16)
        .background(AppColor.darkGrayBackground.color.opacity(0.5))
        .cornerRadius(12)
    }

    // MARK: - Modern Generate Button Section
    private var modernGenerateButtonSection: some View {
        Button(action: {
            scriptManager.generateScript()
        }) {
            HStack(spacing: 10) {
                if scriptManager.isGenerating {
                    ProgressView()
                        .scaleEffect(0.8)
                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                } else {
                    Image(systemName: "wand.and.stars")
                        .font(.system(size: 16, weight: .semibold))
                }

                Text(scriptManager.isGenerating ? "Generating Script..." : "Generate Script")
                    .font(.system(size: 16, weight: .semibold))
            }
            .padding(.horizontal, 24)
            .padding(.vertical, 14)
            .frame(maxWidth: .infinity)
            .background(AppColor.youtubeRed.color)
            .foregroundColor(.white)
            .cornerRadius(10)
            .shadow(color: AppColor.youtubeRed.color.opacity(0.3), radius: 6, x: 0, y: 3)
        }
        .buttonStyle(.plain)
        .disabled(scriptManager.isGenerating || scriptManager.topic.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty)
        .opacity(scriptManager.isGenerating || scriptManager.topic.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty ? 0.6 : 1.0)
    }
}

struct ScriptTypeSelector: View {
    @Binding var selectedType: ScriptType

    let columns = [
        GridItem(.adaptive(minimum: 140), spacing: 12)
    ]

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Video Type")
                .font(AppFontStyle.headline.style.weight(.semibold))
                .foregroundColor(AppColor.primary.color)

            LazyVGrid(columns: columns, spacing: 12) {
                ForEach(ScriptType.allCases, id: \.self) { type in
                    Button(action: {
                        selectedType = type
                    }) {
                        VStack(spacing: 8) {
                            Image(systemName: type.icon)
                                .font(.system(size: 24))
                                .foregroundColor(selectedType == type ? .white : AppColor.youtubeRed.color)

                            Text(type.rawValue)
                                .font(AppFontStyle.caption1.style.weight(.semibold))
                                .foregroundColor(selectedType == type ? .white : AppColor.primary.color)
                                .multilineTextAlignment(.center)
                        }
                        .padding(12)
                        .frame(minHeight: 80)
                        .frame(maxWidth: .infinity)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(selectedType == type ? AppColor.youtubeRed.color : AppColor.darkGrayBackground.color)
                        )
                        .overlay(
                            RoundedRectangle(cornerRadius: 12)
                                .stroke(
                                    selectedType == type ? AppColor.youtubeRed.color : AppColor.grayText.color.opacity(0.2),
                                    lineWidth: selectedType == type ? 2 : 1
                                )
                        )
                    }
                    .buttonStyle(.plain)
                }
            }

            if selectedType != .educational {
                Text(selectedType.description)
                    .font(AppFontStyle.caption1.style)
                    .foregroundColor(AppColor.grayText.color)
                    .padding(.top, 4)
            }
        }
    }
}

#Preview {
    ScriptWriterView()
}
