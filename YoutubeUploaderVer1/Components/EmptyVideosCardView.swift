//
//  EmptyVideosCardView.swift
//  YoutubeUploaderVer1
//
//  Created by <PERSON><PERSON><PERSON><PERSON> B on 01/05/25.
//

import SwiftUI

struct EmptyVideosCardView: View {
    var title: String
    var description: String

    var body: some View {
        VStack(spacing: 24) {
            // Modern icon with gradient background
            ZStack {
                Circle()
                    .fill(
                        LinearGradient(
                            colors: [AppColor.youtubeRed.color.opacity(0.1), AppColor.accentBlue.color.opacity(0.1)],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(width: 120, height: 120)

                Image(systemName: "film.stack")
                    .font(.system(size: 48, weight: .medium))
                    .foregroundColor(AppColor.youtubeRed.color)
            }

            VStack(spacing: 12) {
                Text(title)
                    .font(.system(size: 24, weight: .bold))
                    .foregroundColor(AppColor.textPrimary.color)

                Text(description)
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(AppColor.textSecondary.color)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, 40)
            }
        }
        .padding(40)
        .frame(maxWidth: 600)
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(AppColor.surfacePrimary.color.opacity(0.8))
                .overlay(
                    RoundedRectangle(cornerRadius: 20)
                        .stroke(AppColor.borderPrimary.color.opacity(0.2), lineWidth: 1)
                )
        )
        .shadow(color: Color.black.opacity(0.1), radius: 12, x: 0, y: 6)
    }
}



