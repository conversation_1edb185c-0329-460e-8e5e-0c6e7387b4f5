//
//  YTCreatorSideNavBar.swift
//  YoutubeUploaderVer1
//
//  Created by <PERSON><PERSON><PERSON>  on 07/04/25.
//

import SwiftUI


struct YTCreatorSideNavBar: View {
    @Binding var selectedTab: CreatorTab
    @Binding var isCollapsed: Bool
    @StateObject private var processMonitor = ProcessMonitoringService.shared

    private let expandedWidth: CGFloat = 240
    private let collapsedWidth: CGFloat = 72

    private var currentWidth: CGFloat {
        isCollapsed ? collapsedWidth : expandedWidth
    }

    var body: some View {
        VStack(alignment: .leading, spacing: 4) {
            // Sidebar Header with Toggle Button
            HStack {
                if !isCollapsed {
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Navigation")
                            .font(.system(size: 11, weight: .medium))
                            .foregroundColor(AppColor.textTertiary.color)
                            .textCase(.uppercase)
                            .tracking(0.5)
                    }
                    .transition(.opacity.combined(with: .scale(scale: 0.8)))
                }

                Spacer()

                // Sidebar Toggle Button
                Button(action: {
                    withAnimation(.easeInOut(duration: 0.3)) {
                        isCollapsed.toggle()
                    }
                }) {
                    Image(systemName: isCollapsed ? "chevron.right" : "chevron.left")
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(AppColor.textSecondary.color)
                        .frame(width: 20, height: 20)
                        .background(
                            Circle()
                                .fill(AppColor.surfacePrimary.color)
                                .overlay(
                                    Circle()
                                        .stroke(AppColor.borderPrimary.color.opacity(0.3), lineWidth: 1)
                                )
                        )
                }
                .buttonStyle(PlainButtonStyle())
                .help(isCollapsed ? "Expand sidebar" : "Collapse sidebar")
            }
            .padding(.horizontal, isCollapsed ? 8 : 20)
            .padding(.top, 16)
            .padding(.bottom, 8)

            // Divider (only when expanded)
            if !isCollapsed {
                Divider()
                    .background(AppColor.borderPrimary.color.opacity(0.2))
                    .padding(.horizontal, 20)
                    .padding(.bottom, 4)
                    .transition(.opacity)
            }

            // Clean Navigation Items
            ForEach(CreatorTab.allCases, id: \.self) { tab in
                Button(action: {
                    requestTabChange(to: tab)
                }) {
                    if isCollapsed {
                        // Collapsed state - icon only with centered layout
                        VStack(spacing: 4) {
                            Image(systemName: tab.iconName)
                                .font(.system(size: 18, weight: .medium))
                                .foregroundColor(
                                    selectedTab == tab
                                    ? AppColor.youtubeRed.color
                                    : AppColor.textSecondary.color
                                )
                                .frame(width: 24, height: 24)
                        }
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 12)
                        .contentShape(Rectangle())
                    } else {
                        // Expanded state - icon and text
                        HStack(spacing: 12) {
                            // Simple icon
                            Image(systemName: tab.iconName)
                                .font(.system(size: 16, weight: .medium))
                                .foregroundColor(
                                    selectedTab == tab
                                    ? AppColor.youtubeRed.color
                                    : AppColor.textSecondary.color
                                )
                                .frame(width: 24, height: 24)

                            // Tab title
                            Text(tab.rawValue)
                                .font(.system(size: 14, weight: selectedTab == tab ? .semibold : .medium))
                                .foregroundColor(
                                    selectedTab == tab
                                    ? AppColor.textPrimary.color
                                    : AppColor.textSecondary.color
                                )
                                .transition(.opacity.combined(with: .move(edge: .leading)))

                            Spacer()
                        }
                        .padding(.horizontal, 20)
                        .padding(.vertical, 10)
                        .contentShape(Rectangle())
                    }
                }
                .buttonStyle(PlainButtonStyle())
                .background(
                    RoundedRectangle(cornerRadius: 8)
                        .fill(
                            selectedTab == tab
                            ? AppColor.youtubeRed.color.opacity(0.1)
                            : Color.clear
                        )
                )
                .padding(.horizontal, isCollapsed ? 8 : 12)
                .padding(.vertical, 2)
                .help(isCollapsed ? tab.rawValue : "") // Show tooltip when collapsed
            }

            Spacer()
        }
        .frame(width: currentWidth)
        .animation(.easeInOut(duration: 0.3), value: isCollapsed)
        .background(
            ZStack {
                // Clean background
                Rectangle()
                    .fill(AppColor.surfacePrimary.color)
                    .background(.regularMaterial)

                // Simple right border
                HStack {
                    Spacer()
                    Rectangle()
                        .fill(AppColor.borderPrimary.color.opacity(0.2))
                        .frame(width: 1)

                }
            }
        )
        .shadow(color: Color.black.opacity(0.1), radius: 8, x: 2, y: 0)
        .clipped()
        .alert("⚠️ Active Processes", isPresented: $processMonitor.showProcessAlert) {
            Button("Cancel Processes & Change Tab", role: .destructive) {
                processMonitor.confirmProcessAlert()
            }
            Button("Stay Here", role: .cancel) {
                processMonitor.cancelProcessAlert()
            }
        } message: {
            Text("The following processes are currently running:\n\n• \(processMonitor.activeProcessDescriptions.joined(separator: "\n• "))\n\nChanging tabs will cancel these processes and you may lose your progress. Do you want to continue?")
        }
        .onReceive(NotificationCenter.default.publisher(for: NSNotification.Name("ProceedWithTabChange"))) { notification in
            if let newTab = notification.object as? CreatorTab {
                withAnimation(.easeInOut(duration: 0.15)) {
                    selectedTab = newTab
                }
            }
        }
    }

    // MARK: - Tab Change Request
    private func requestTabChange(to newTab: CreatorTab) {
        // If it's the same tab, do nothing
        guard newTab != selectedTab else { return }

        // Check if we need to intercept the tab change
        processMonitor.requestTabChange(to: newTab) { allowed in
            if allowed {
                // No active processes or not on upload view - allow immediate change
                withAnimation(.easeInOut(duration: 0.15)) {
                    selectedTab = newTab
                }
            } else {
                // Has active processes - the ProcessMonitoringService will handle the alert
                // Don't change the tab here - let the alert system handle it
            }
        }
    }
}
