//
//  YTCreatorSideNavBar.swift
//  YoutubeUploaderVer1
//
//  Created by <PERSON><PERSON><PERSON>  on 07/04/25.
//

import SwiftUI


struct YTCreatorSideNavBar: View {
    @Binding var selectedTab: CreatorTab
    

    private let expandedWidth: CGFloat = 240
    private let collapsedWidth: CGFloat = 72

    var body: some View {
        VStack(alignment: .leading, spacing: 4) {
            // Clean Navigation Header
            VStack(alignment: .leading, spacing: 8) {
                Text("Navigation")
                    .font(.system(size: 11, weight: .medium))
                    .foregroundColor(AppColor.textTertiary.color)
                    .textCase(.uppercase)
                    .tracking(0.5)

                Divider()
                    .background(AppColor.borderPrimary.color.opacity(0.2))
            }
            .padding(.horizontal, 20)
            .padding(.top, 20)
            .padding(.bottom, 4)

            // Clean Navigation Items
            ForEach(CreatorTab.allCases, id: \.self) { tab in
                Button(action: {
                    withAnimation(.easeInOut(duration: 0.15)) {
                        selectedTab = tab
                    }
                }) {
                    HStack(spacing: 12) {
                        // Simple icon
                        Image(systemName: tab.iconName)
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(
                                selectedTab == tab
                                ? AppColor.youtubeRed.color
                                : AppColor.textSecondary.color
                            )
                            .frame(width: 24, height: 24)

                        // Tab title
                        Text(tab.rawValue)
                            .font(.system(size: 14, weight: selectedTab == tab ? .semibold : .medium))
                            .foregroundColor(
                                selectedTab == tab
                                ? AppColor.textPrimary.color
                                : AppColor.textSecondary.color
                            )

                        Spacer()
                    }
                    .padding(.horizontal, 20)
                    .padding(.vertical, 10)
                    .contentShape(Rectangle())
                }
                .buttonStyle(PlainButtonStyle())
                .background(
                    RoundedRectangle(cornerRadius: 8)
                        .fill(
                            selectedTab == tab
                            ? AppColor.youtubeRed.color.opacity(0.1)
                            : Color.clear
                        )
                )
                .padding(.horizontal, 12)
                .padding(.vertical, 2)
            }

            Spacer()
        }
        .frame(width: 240)
        .background(
            ZStack {
                // Clean background
                Rectangle()
                    .fill(AppColor.surfacePrimary.color)
                    .background(.regularMaterial)

                // Simple right border
                HStack {
                    Spacer()
                    Rectangle()
                        .fill(AppColor.borderPrimary.color.opacity(0.2))
                        .frame(width: 1)
                       
                }
            }
        )
    }
}
