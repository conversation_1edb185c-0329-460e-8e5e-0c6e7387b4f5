//
//  PlayListsView.swift
//  YoutubeUploaderVer1
//
//  Created by Shashank B on 09/04/25.
//

import SwiftUI


struct PlayListsView: View {
    @EnvironmentObject var navigationCoordinator:NavigationCoordinator
    @StateObject private var viewModel = PlaylistViewModel.shared
    
    
    
    var body: some View {
        VStack(alignment: .leading, spacing: 0) {
            // Header
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(TextConstants.Playlists.playlistsHeaderTitle)
                        .font(.system(size: 24, weight: .bold))
                    
                    Text(TextConstants.Playlists.playlistsHeaderSubtitle)
                        .font(.system(size: 14))
                        .foregroundColor(AppColor.grayText.color)
                }
                
                Spacer()
            }
            
            .padding(.bottom, 16)
            ScrollView {
                if viewModel.isLoading {
                    VStack(spacing: 16) {
                        ForEach(0..<3, id: \.self) { _ in
                            HStack(spacing: 16) {
                                ForEach(0..<4, id: \.self) { _ in
                                    CardViewSkeleton()
                                        .blinking(duration: 0.75)
                                }
                            }
                        }
                    }
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                }else if let error = viewModel.errorMessage {
                    Text("⚠️ \(error)")
                        .foregroundColor(.red)
                        .frame(maxWidth: .infinity, maxHeight: .infinity)
                } else if viewModel.playlists.isEmpty {
                    Spacer()
                    HStack {
                        Spacer()
                        EmptyVideosCardView(title: TextConstants.Playlists.noPlaylistsTitle, description: TextConstants.Playlists.noPlaylistsDescription)
                        Spacer()
                    }
                    Spacer()
                }
                else{
                    LazyVGrid(columns: [
                        GridItem(.flexible(), spacing: 16),
                        GridItem(.flexible(), spacing: 16),
                        GridItem(.flexible(), spacing: 16),
                        GridItem(.flexible(), spacing: 16)
                    ], spacing: 16) {
                        ForEach(viewModel.playlists) { playlist in
                            Button(action: {
                                viewModel.playlistTapped(playlist)
                                navigationCoordinator.navigateToPlaylistDetailView()
                                
                            }) {
                                PlaylistCard(playlist: playlist)
                            }
                            .buttonStyle(PlainButtonStyle())
                            
                        }
                    }
                    .padding(.bottom, 16)
                }
                
                if let _ = viewModel.nextPageToken, !viewModel.isLoading , viewModel.playlists.count >= 8{
                    Button(action: {
                        Task {
                            await viewModel.loadNextPage()
                        }
                    }) {
                        HStack {
                            Text("Load more")
                                .font(AppFontStyle.callout.style.bold())
                                .foregroundStyle(AppColor.youtubeRed.color)
                            Image(systemName: "arrow.down.circle.fill")
                                .foregroundStyle(AppColor.youtubeRed.color)
                        }
                        
                        .padding(.horizontal, 16)
                        .padding(.vertical, 8)
                        .clipShape(RoundedRectangle(cornerRadius: 10))
                        .shadow(color: AppColor.youtubeRed.color.opacity(0.3), radius: 6, x: 0, y: 2)
                    }
                    .buttonStyle(PlainButtonStyle())
                    .padding(.trailing, 30)
                    .frame(maxWidth: .infinity, alignment: .trailing)
                }
            }
            
        }
        .frame(minWidth: 800, minHeight: 600)
        .task {
            await viewModel.fetchUserPlaylists()
        }
       
    }
}



// Extensions to help with date formatting if needed
extension String {
    func toDate() -> Date? {
        let formatter = ISO8601DateFormatter()
        return formatter.date(from: self)
    }
}

extension Date {
    func timeAgo() -> String {
        let calendar = Calendar.current
        let now = Date()
        let components = calendar.dateComponents([.day, .weekOfMonth, .month], from: self, to: now)
        
        if let month = components.month, month > 0 {
            return month == 1 ? TextConstants.Playlists.oneMonthAgo : "\(month) \(TextConstants.Playlists.monthsAgo)"
        } else if let week = components.weekOfMonth, week > 0 {
            return week == 1 ? TextConstants.Playlists.oneWeekAgo : "\(week) \(TextConstants.Playlists.weeksAgo)"
        } else if let day = components.day, day > 0 {
            return day == 1 ? TextConstants.Playlists.oneDayAgo : "\(day) \(TextConstants.Playlists.daysAgo)"
        } else {
            return TextConstants.Playlists.today
        }
    }
}  


#Preview {
    PlayListsView()
}
