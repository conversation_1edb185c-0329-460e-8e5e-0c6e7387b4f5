//
//  VideosUploadView.swift
//  YoutubeUploaderVer1
//
//  Created by <PERSON><PERSON><PERSON>  on 26/04/25.
//

import SwiftUI
import AVKit
import UniformTypeIdentifiers
import PhotosUI
import AVFoundation
import Speech

enum ActiveAlert: Identifiable {
    case uploadError(title: String, message: String)
    case uploadSuccess
    case transcriptionError(title: String, message: String)
    
    var id: String {
        switch self {
        case .uploadError: return "uploadError"
        case .uploadSuccess: return "uploadSuccess"
        case .transcriptionError: return "transcriptionError"
        }
    }
}



struct VideosUploadView: View {
    @ObservedObject var viewModel = VideoUploadViewModel()
    @StateObject private var audioTranscriptionManager = AudioTranscriptionManager()
    @State private var isDropTargeted: Bool = false
    @State private var showFileImporter: Bool = false
    @State private var selectedFileURL: URL?
    @State private var errorMessage: String?
    @State private var errorTitle: String?
    @State private var uploadTask: Task<Void, Never>? = nil
    @State private var showErrorAlert = false
    @State private var showValidation: Bool = false
    @State private var isFormValid: Bool = false
    @State private var showUploadSuccessAlert = false
    @State private var expandedAIOption: AIOption? = nil
    private let allowedVideoTypes: [UTType] = [.movie, .video]
    let privacyOptions = ["Public", "Private", "Unlisted"]
    let localAIService = LocalAIService.shared
    @State private var activeAlert: ActiveAlert?
    var videoURL:URL? = nil
    
    init(videoURL: URL? = nil) {
        self.videoURL = videoURL
    }
    
    //for audio and transcript
    private var isConvertingToAudio: Bool { audioTranscriptionManager.isConvertingToAudio }
    private var isTranscribing: Bool { audioTranscriptionManager.isTranscribing }
    private var audioFileURL: URL? { audioTranscriptionManager.audioFileURL }
    private var showAudioSection: Bool { audioTranscriptionManager.showAudioSection }
    private var showTranscriptSection: Bool { audioTranscriptionManager.showTranscriptSection }
    private var transcriptText: [(TimeInterval, TimeInterval, String)] { audioTranscriptionManager.transcriptText }
    @State private var enableAIEnhancement: Bool = false
    
    
    var screenWidth: CGFloat {
        NSScreen.main?.frame.width ?? 1440
    }
    
    var screenHeight: CGFloat {
        NSScreen.main?.frame.height ?? 900
    }
    
    
    var body: some View {
        ScrollView {
            VStack(alignment: .leading, spacing: 32) {
                // MARK: - Video Upload Header
                VStack(alignment: .leading, spacing: 8) {
                    Text(TextConstants.UploadVideos.title)
                        .font(AppFontStyle.largeTitle.style.weight(.bold))
                        .foregroundColor(AppColor.primary.color)
                    
                    Text(TextConstants.UploadVideos.subtitle)
                        .font(AppFontStyle.subheadline.style)
                        .foregroundColor(AppColor.grayText.color)
                }
                
                .padding(.horizontal, 4)
                
                // MARK: - Content Area
                HStack(alignment: .top, spacing: 28) {
                    // MARK: - Media Upload Section
                    VStack(spacing: 20) {
                        if selectedFileURL == nil {
                            // Empty state - Upload area
                            VStack(spacing: 24) {
                                // Upload icon
                                Circle()
                                    .fill(AppColor.darkBackground.color)
                                    .frame(width: 100, height: 100)
                                    .shadow(color: isDropTargeted ? AppColor.primary.color.opacity(0.5) : Color.black.opacity(0.2),
                                            radius: isDropTargeted ? 15 : 8)
                                    .scaleEffect(isDropTargeted ? 1.08 : 1)
                                    .animation(.easeInOut(duration: 0.3), value: isDropTargeted)
                                    .overlay(
                                        Image(systemName: "film")
                                            .font(.system(size: 36, weight: .medium))
                                            .foregroundColor(AppColor.primary.color.opacity(0.9))
                                    )
                                // Removed unnecessary ZStack
                                
                                // Upload instructions
                                Text("Click to select or Drag the video file here")
                                    .font(AppFontStyle.callout.style.weight(.medium))
                                    .foregroundColor(AppColor.grayText.color)
                                    .multilineTextAlignment(.center)
                                    .padding(.horizontal, 20)
                                
                                // Select video button
                                Button {
                                    showFileImporter = true
                                } label: {
                                    HStack(spacing: 8) {
                                        Image(systemName: "arrow.up.doc.fill")
                                            .font(.system(size: 16))
                                        Text("Select Video")
                                            .font(AppFontStyle.body.style.weight(.semibold))
                                    }
                                    .padding(.horizontal, 28)
                                    .padding(.vertical, 14)
                                    .background(
                                        RoundedRectangle(cornerRadius: 12)
                                            .fill(AppColor.youtubeRed.color)
                                    )
                                    .foregroundColor(.white)
                                    .shadow(color: AppColor.youtubeRed.color.opacity(0.4), radius: 8, x: 0, y: 4)
                                }
                                .buttonStyle(.plain)
                                
                                // Removed redundant shadow modifier
                            }
                            .padding(32)
                            .frame(maxWidth: .infinity)
                            .frame(minHeight: screenHeight * 0.4)
                            .background(
                                RoundedRectangle(cornerRadius: 24)
                                    .strokeBorder(
                                        style: StrokeStyle(
                                            lineWidth: isDropTargeted ? 2.5 : 2,
                                            dash: [8]
                                        )
                                    )
                                    .foregroundColor(
                                        isDropTargeted ?
                                        AppColor.primary.color.opacity(0.8) :
                                            AppColor.grayText.color.opacity(0.5)
                                    )
                            )
                            .background(
                                RoundedRectangle(cornerRadius: 24)
                                    .fill(AppColor.darkBackground.color.opacity(0.3))
                            )
                            .animation(.easeInOut(duration: 0.3), value: isDropTargeted)
                            .onDrop(of: [.fileURL], isTargeted: nil) { providers in
                                if let provider = providers.first(where: { $0.canLoadObject(ofClass: URL.self) }) {
                                    _ = provider.loadObject(ofClass: URL.self) { object, error in
                                        guard let url = object else {
                                            DispatchQueue.main.async {
                                                errorMessage = "Dropped item is not a valid URL."
                                            }
                                            return
                                        }
                                        
                                        do {
                                            let resourceValues = try url.resourceValues(forKeys: [.contentTypeKey])
                                            if let contentType = resourceValues.contentType,
                                               allowedVideoTypes.contains(where: { contentType.conforms(to: $0) }) {
                                                DispatchQueue.main.async {
                                                    processSelectedFile(url: url)
                                                }
                                            } else {
                                                DispatchQueue.main.async {
                                                    errorMessage = "Only video files are allowed."
                                                }
                                            }
                                        } catch {
                                            DispatchQueue.main.async {
                                                errorMessage = "Could not determine file type."
                                            }
                                        }
                                    }
                                    return true
                                }
                                return false
                            }
                            
                            
                            .fileImporter(
                                isPresented: $showFileImporter,
                                allowedContentTypes: allowedVideoTypes,
                                allowsMultipleSelection: false
                            ) { result in
                                handleFileImport(result: result)
                            }
                            
                            // Error message
                            if let error = errorMessage {
                                Text("Error: \(error)")
                                    .foregroundColor(AppColor.youtubeRed.color)
                                    .font(AppFontStyle.subheadline.style.weight(.medium))
                                    .padding(.vertical, 10)
                                    .padding(.horizontal, 16)
                                    .background(
                                        RoundedRectangle(cornerRadius: 8)
                                            .fill(AppColor.youtubeRed.color.opacity(0.1))
                                    )
                                    .padding(.top, 12)
                            }
                            
                        } else if let selectedFileURL {
                            // MARK: - Video Preview
                            ZStack(alignment: .topTrailing) {
                                VideoPlayer(player: AVPlayer(url: selectedFileURL))
                                    .clipShape(RoundedRectangle(cornerRadius: 20))
                                    .shadow(color: Color.black.opacity(0.2), radius: 12, x: 0, y: 6)
                                    .frame(height: screenHeight * 0.4)
                                    .overlay(
                                        RoundedRectangle(cornerRadius: 20)
                                            .stroke(AppColor.primary.color.opacity(0.1), lineWidth: 1)
                                    )
                                
                                // Delete button
                                Button(role: .destructive) {
                                    removeSelectedVideo()
                                    resetToFreshState()
                                } label: {
                                    Image(systemName: "trash.circle.fill")
                                        .font(AppFontStyle.largeTitle.style)
                                        .foregroundColor(AppColor.youtubeRed.color)
                                        .background(Circle().fill(AppColor.primary.color))
                                        .shadow(color: Color.black.opacity(0.2), radius: 4, x: 0, y: 2)
                                }
                                .buttonStyle(.plain)
                                .padding(12)
                                .help("Remove video")
                            }
                            // MARK: - Video Processing Status
                            VStack(spacing: 12) {
                                // Audio Processing Status
                                HStack(spacing: 12) {
                                    if isConvertingToAudio {
                                        ProgressView()
                                            .scaleEffect(0.8)
                                            .progressViewStyle(CircularProgressViewStyle(tint: AppColor.primary.color))
                                    } else if audioTranscriptionManager.showAudioSection {
                                        Image(systemName: "checkmark.circle.fill")
                                            .foregroundColor(.green)
                                            .font(.system(size: 16))
                                    } else {
                                        Image(systemName: "waveform")
                                            .foregroundColor(AppColor.grayText.color)
                                            .font(.system(size: 16))
                                    }
                                    
                                    Text(getAudioStatusText())
                                        .font(AppFontStyle.body.style.weight(.medium))
                                        .foregroundColor(AppColor.primary.color)
                                    
                                    Spacer()
                                }
                                
                                // Transcription Processing Status
                                HStack(spacing: 12) {
                                    if isTranscribing {
                                        ProgressView()
                                            .scaleEffect(0.8)
                                            .progressViewStyle(CircularProgressViewStyle(tint: AppColor.primary.color))
                                    } else if audioTranscriptionManager.showTranscriptSection {
                                        Image(systemName: "checkmark.circle.fill")
                                            .foregroundColor(.green)
                                            .font(.system(size: 16))
                                    } else {
                                        Image(systemName: "text.bubble")
                                            .foregroundColor(AppColor.grayText.color)
                                            .font(.system(size: 16))
                                    }
                                    
                                    Text(getTranscriptionStatusText())
                                        .font(AppFontStyle.body.style.weight(.medium))
                                        .foregroundColor(AppColor.primary.color)
                                    
                                    Spacer()
                                }
                            }
                            .padding(.horizontal, 20)
                            .padding(.vertical, 16)
                            .background(
                                RoundedRectangle(cornerRadius: 12)
                                    .fill(AppColor.darkGrayBackground.color.opacity(0.5))
                            )
                            .overlay(
                                RoundedRectangle(cornerRadius: 12)
                                    .stroke(AppColor.primary.color.opacity(0.1), lineWidth: 1)
                            )
                            
                            
                            Button {
                                if audioTranscriptionManager.showTranscriptSection {
                                    // If transcript is already available, just enable AI enhancement
                                    enableAIEnhancement = true
                                } else {
                                    // Otherwise, extract audio, transcribe, and then enable AI enhancement
                                    audioTranscriptionManager.enableAIEnhancement(for: self.selectedFileURL!) { success in
                                        if success {
                                            enableAIEnhancement = true
                                        }
                                    }
                                }
                            }label: {
                                HStack(spacing: 8) {
                                    Image(systemName: "wand.and.stars")
                                        .font(.system(size: 16))
                                    Text("Explore AI Enhancement")
                                        .font(AppFontStyle.body.style.weight(.semibold))
                                }
                                .padding(.horizontal, 20)
                                .padding(.vertical, 12)
                                .background(
                                    RoundedRectangle(cornerRadius: 12)
                                        .fill(AppColor.darkGrayBackground.color)
                                )
                                .foregroundColor(AppColor.primary.color)
                                .overlay(
                                    RoundedRectangle(cornerRadius: 12)
                                        .stroke(AppColor.primary.color.opacity(0.2), lineWidth: 1)
                                )
                            }
                            .buttonStyle(.plain)
                            .disabled(isConvertingToAudio || isTranscribing)
                            .opacity(isConvertingToAudio || isTranscribing ? 0.6 : 1.0)
                        }
                    }
                }
                .frame(maxWidth: .infinity)
                if enableAIEnhancement{
                    VStack(alignment: .leading, spacing: 16){
                        Text("AI Enhancement Options")
                            .font(AppFontStyle.title1.style.weight(.bold))
                            .foregroundColor(AppColor.primary.color)
                        ForEach(AIOption.allCases){
                            option in
                            VStack(alignment: .leading, spacing: 0){
                                Button(action: {
                                    withAnimation(.easeInOut(duration: 0.25)) {
                                        if expandedAIOption == option {
                                            expandedAIOption = nil
                                        } else {
                                            expandedAIOption = option
                                        }
                                    }
                                }){
                                    AIOptionsCard(
                                        iconName: option.icon,
                                        title: option.title,
                                        subtitle: option.subtitle,
                                        isSelected: expandedAIOption == option
                                    )
                                }
                                .buttonStyle(PlainButtonStyle())
                                if expandedAIOption == option{
                                    AIDetailView(
                                        option: option,
                                        transcriptItems: transcriptText,
                                        selectedFileURL: selectedFileURL,
                                        videoTitle: viewModel.title,
                                        videoDescription: viewModel.description,
                                        videoCategory: viewModel.privacyStatus
                                    )
                                    .padding(.top, 12)
                                }
                            }
                        }
                        
                    }
                }
                AudioFileSection(
                    isVisible: Binding(
                        get: { audioTranscriptionManager.showAudioSection },
                        set: { audioTranscriptionManager.showAudioSection = $0 }
                    ),
                    audioFileURL: audioTranscriptionManager.audioFileURL,
                    transcriptText: audioTranscriptionManager.transcriptText
                )
                TranscriptSection(
                    isVisible: Binding(
                        get: { audioTranscriptionManager.showTranscriptSection },
                        set: { audioTranscriptionManager.showTranscriptSection = $0 }
                    ),
                    transcriptItems: audioTranscriptionManager.transcriptText,
                    onExport: {
                        exportTranscript(audioTranscriptionManager.transcriptText)
                    }
                )
                .padding(.bottom, 32)
                
                VideoDetailsFormView(viewModel: viewModel, showValidation: showValidation,transcript: transcriptText)
                
                
                
            }
            
//            if enableAIEnhancement{
//                VStack(alignment: .leading, spacing: 16){
//                    Text("AI Enhancement Options")
//                        .font(AppFontStyle.title1.style.weight(.bold))
//                        .foregroundColor(AppColor.primary.color)
//                    ForEach(AIOption.allCases){
//                        option in
//                        VStack(alignment: .leading, spacing: 0){
//                            Button(action: {
//                                withAnimation(.easeInOut(duration: 0.25)) {
//                                    if expandedAIOption == option {
//                                        expandedAIOption = nil
//                                    } else {
//                                        expandedAIOption = option
//                                    }
//                                }
//                            }){
//                                AIOptionsCard(
//                                    iconName: option.icon,
//                                    title: option.title,
//                                    subtitle: option.subtitle,
//                                    isSelected: expandedAIOption == option
//                                )
//                            }
//                            .buttonStyle(PlainButtonStyle())
//                            if expandedAIOption == option{
//                                AIDetailView(option: option, transcriptItems: transcriptText,selectedFileURL:selectedFileURL)
//                                    .padding(.top, 12)
//                            }
//                        }
//                    }
//                    
//                }
//            }
            
            
            // MARK: - Audio Section (conditionally shown)
//            AudioFileSection(
//                isVisible: Binding(
//                    get: { audioTranscriptionManager.showAudioSection },
//                    set: { audioTranscriptionManager.showAudioSection = $0 }
//                ),
//                audioFileURL: audioTranscriptionManager.audioFileURL,
//                transcriptText: audioTranscriptionManager.transcriptText
//            )
            // MARK: - Transcript Section (conditionally shown)
//            TranscriptSection(
//                isVisible: Binding(
//                    get: { audioTranscriptionManager.showTranscriptSection },
//                    set: { audioTranscriptionManager.showTranscriptSection = $0 }
//                ),
//                transcriptItems: audioTranscriptionManager.transcriptText,
//                onExport: {
//                    exportTranscript(audioTranscriptionManager.transcriptText)
//                }
//            )
//            .padding(.bottom, 32)
            //                if enableAIEnhancement{
            //                    VStack(alignment: .leading, spacing: 16){
            //                        Text("AI Enhancement Options")
            //                            .font(AppFontStyle.title1.style.weight(.bold))
            //                            .foregroundColor(AppColor.primary.color)
            //                        ForEach(AIOption.allCases){
            //                            option in
            //                            VStack(alignment: .leading, spacing: 0){
            //                                Button(action: {
            //                                    withAnimation(.easeInOut(duration: 0.25)) {
            //                                        if expandedAIOption == option {
            //                                            expandedAIOption = nil
            //                                        } else {
            //                                            expandedAIOption = option
            //                                        }
            //                                    }
            //                                }){
            //                                    AIOptionsCard(
            //                                        iconName: option.icon,
            //                                        title: option.title,
            //                                        subtitle: option.subtitle,
            //                                        isSelected: expandedAIOption == option
            //                                    )
            //                                }
            //                                .buttonStyle(PlainButtonStyle())
            //                                if expandedAIOption == option{
            //                                    AIDetailView(option: option, transcriptItems: transcriptText,selectedFileURL:selectedFileURL)
            //                                        .padding(.top, 12)
            //                                }
            //                            }
            //                        }
            //
            //                    }
            //                }
            
            // MARK: - Action Controls
            if viewModel.isUploading {
                HStack(spacing: 12) {
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                    
                    Text("Uploading...")
                        .font(AppFontStyle.headline.style.weight(.medium))
                        .foregroundColor(.white)
                }
                .padding(.vertical, 14)
                .frame(maxWidth: .infinity)
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .fill(AppColor.youtubeRed.color)
                )
                .shadow(color: AppColor.youtubeRed.color.opacity(0.4), radius: 8, x: 0, y: 4)
            } else {
                // Upload button
                if viewModel.uploadSuccess{
                    Button {
                        // Action here
                    } label: {
                        HStack(spacing: 8) {
                            Image(systemName: "checkmark.circle.fill")
                                .font(.system(size: 18))
                            Text("Upload completed")
                                .font(AppFontStyle.headline.style.weight(.semibold))
                        }
                        .padding(.vertical, 18)
                        .frame(maxWidth: .infinity)
                        .background(
                            RoundedRectangle(cornerRadius: 16)
                                .fill(Color.green)
                        )
                        .foregroundColor(.white)
                        .shadow(
                            color: Color.green.opacity(0.4),
                            radius: 8, x: 0, y: 4
                        )
                    }
                    .buttonStyle(.plain)
                    
                    
                }
                else{
                    Button {
                        validateForm()
                        if isFormValid {
                            Task {
                                await viewModel.uploadVideo()
                                if viewModel.errorMessage != nil {
                                    //                                        showErrorAlert = true
                                    activeAlert = .uploadError(title: errorTitle ?? "Upload Failed",
                                                               message: viewModel.errorMessage ?? "An unknown error occurred.")
                                    
                                } else if viewModel.uploadSuccess {
                                    //                                        showUploadSuccessAlert = true
                                    activeAlert = .uploadSuccess
                                }
                            }
                        }
                    } label: {
                        HStack(spacing: 8) {
                            Image(systemName: "arrow.up.doc.fill")
                                .font(.system(size: 18))
                            Text("Upload Video")
                                .font(AppFontStyle.headline.style.weight(.semibold))
                        }
                        .padding(.vertical, 18)
                        .frame(maxWidth: .infinity)
                        .background(
                            RoundedRectangle(cornerRadius: 16)
                                .fill(AppColor.youtubeRed.color)
                        )
                        .foregroundColor(.white)
                        .shadow(
                            color: AppColor.youtubeRed.color.opacity(0.4),
                            radius: 8, x: 0, y: 4
                        )
                    }
                    .buttonStyle(.plain)
                    
                }
            }
        }
        .padding(.horizontal, 24)
        .padding(.vertical, 32)
        
        
        .onAppear{
            Task{
                await localAIService.initializeModel()
            }
        }
        .background(AppColor.darkBackground.color.opacity(0.1))
        .onChange(of: showUploadSuccessAlert) { newValue in
            if newValue {
                DispatchQueue.main.asyncAfter(deadline: .now() + 3) {
                    showUploadSuccessAlert = false
                    resetToFreshState()
                }
            }
        }
        .onChange(of: audioTranscriptionManager.showErrorAlert) { newValue in
            if newValue {
                activeAlert = .transcriptionError(
                    title: audioTranscriptionManager.errorTitle ?? "Something went wrong",
                    message: audioTranscriptionManager.errorMessage ?? "An unknown error occurred."
                )
                audioTranscriptionManager.showErrorAlert = false // reset it to prevent retriggering
            }
        }
        .alert(item: $activeAlert) { alert in
            switch alert {
            case .uploadError(let title, let message):
                return Alert(
                    title: Text(title),
                    message: Text(message),
                    dismissButton: .default(Text("OK")) {
                        self.errorTitle = nil
                        viewModel.errorMessage = nil
                    }
                )
                
            case .uploadSuccess:
                return Alert(
                    title: Text("Upload Successful"),
                    message: Text("Your video has been uploaded successfully to YouTube. Processing may take a few minutes and the video will appear shortly in videos section."),
                    dismissButton: .default(Text("OK")) {
                        resetToFreshState()
                    }
                )
                
            case .transcriptionError(let title, let message):
                return Alert(
                    title: Text(title),
                    message: Text(message),
                    dismissButton: .default(Text("OK")) {
                        audioTranscriptionManager.errorTitle = nil
                        audioTranscriptionManager.errorMessage = nil
                    }
                )
            }
        }
        .onAppear {
            if let videoURL = videoURL {
                selectedFileURL = videoURL
                processSelectedFile(url: videoURL)
            }
        }
    }
    
    private func handleFileImport(result: Result<[URL], Error>) {
        switch result {
        case .success(let urls):
            guard let url = urls.first else {
                errorMessage = "Could not get selected file URL."
                return
            }
            processSelectedFile(url: url)
        case .failure(let error):
            errorMessage = "Failed to import file: \(error.localizedDescription)"
        }
    }
    
    private func validateForm() {
        showValidation = true
        
        // Check if required fields are filled
        let isTitleValid = !viewModel.title.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty
        let isDescriptionValid = !viewModel.description.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty
        
        // Check if video is selected
        let isVideoSelected = selectedFileURL != nil
        
        // Update form validity
        isFormValid = isTitleValid && isDescriptionValid && isVideoSelected
        
        // If video is not selected, show an error
        if !isVideoSelected {
            errorMessage = "Please select a video to upload"
        } else {
            errorMessage = nil
        }
    }
    
    private func processSelectedFile(url: URL) {
        self.selectedFileURL = url
        self.errorMessage = nil
        do{
            let videoData = try Data(contentsOf: url)
            viewModel.selectedVideoData = videoData
            
            // Automatically start audio extraction and transcription
            startAutomaticAudioProcessing(for: url)
        }
        catch {
            errorMessage = "Failed to read video data: \(error.localizedDescription)"
        }
    }
    
    private func startAutomaticAudioProcessing(for videoURL: URL) {
        // Reset any previous state
        audioTranscriptionManager.reset()
        
        // Start audio extraction
        audioTranscriptionManager.extractAudio(from: videoURL) {
            // After audio extraction completes, automatically start transcription
            DispatchQueue.main.async {
                if self.audioTranscriptionManager.audioFileURL != nil {
                    // Audio extraction successful, now start transcription
                    self.audioTranscriptionManager.transcribeVideo(from: videoURL)
                }
            }
        }
    }
    private func resetToFreshState() {
        // Reset video selection
        selectedFileURL = nil
        viewModel.selectedVideoData = nil
        errorMessage = nil
        errorTitle = nil

        // Reset form fields
        viewModel.title = ""
        viewModel.description = ""
        viewModel.privacyStatus = privacyOptions.first ?? "Public"
        viewModel.madeForKids = false
        viewModel.notifySubscribers = false

        // Reset upload status
        viewModel.uploadSuccess = false
        viewModel.resetUploadStatus()
        viewModel.cancelUpload()

        // Reset UI state
        showValidation = false
        isFormValid = false
        enableAIEnhancement = false
        expandedAIOption = nil
        activeAlert = nil
        showUploadSuccessAlert = false
        showErrorAlert = false

        // Reset audio transcription completely
        audioTranscriptionManager.reset()

        // Cancel any ongoing upload task
        uploadTask?.cancel()
        uploadTask = nil
    }

    private func removeSelectedVideo() {
        selectedFileURL = nil
        viewModel.selectedVideoData = nil
        errorMessage = nil
        viewModel.resetUploadStatus()
        viewModel.cancelUpload()
        enableAIEnhancement = false
        audioTranscriptionManager.reset()
    }
    
    private func getAudioStatusText() -> String {
        if isConvertingToAudio {
            return "Converting video to audio..."
        } else if audioTranscriptionManager.showAudioSection {
            return "Audio extraction completed"
        } else {
            return "Audio extraction pending"
        }
    }
    
    private func getTranscriptionStatusText() -> String {
        if isTranscribing {
            return "Generating transcript..."
        } else if audioTranscriptionManager.showTranscriptSection {
            return "Transcription completed"
        } else {
            return "Transcription pending"
        }
    }

}

#Preview {
    VideosUploadView()
        .frame(width:NSScreen.main?.frame.width,height:NSScreen.main?.frame.height)
}
