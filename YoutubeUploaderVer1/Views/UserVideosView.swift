//
//  MyVideosList.swift
//  YoutubeUploaderVer1
//
//  Created by Shashan<PERSON> B on 07/04/25.
//

import SwiftUI
import AVKit

struct UserVideosView: View {
    @EnvironmentObject var videoAnalyticsViewModel: YouTubeVideoAnalyticsViewModel
    @State private var searchText: String = ""
    @EnvironmentObject var navigationCoordinator: NavigationCoordinator
    private var filteredVideos: [YouTubeVideo] {
           guard let allVideos = videoAnalyticsViewModel.allUploadedVideos else { return [] }
           if searchText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
               return allVideos
           }
           return allVideos.filter { video in
               video.title.lowercased().contains(searchText.lowercased())
           }
       }

    
    var body: some View {
        VStack(alignment: .leading, spacing: 0) {
            // Header
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Your Videos")
                        .font(.system(size: 24, weight: .bold))
                    
                    Text("Manage and analyze your video content")
                        .font(.system(size: 14))
                        .foregroundColor(AppColor.grayText.color)
                }
                
                Spacer()
                HStack(spacing: 6) {
                    Image(systemName: "magnifyingglass")
                        .font(AppFontStyle.headline.style)
                        .foregroundColor(AppColor.grayText.color)
                        .padding(.leading, 8)
                    
                    TextField("Search videos, playlists...", text: $searchText)
                        .tint(.white)
                        .font(AppFontStyle.headline.style)
                        .frame(height: 36)
                        .textFieldStyle(PlainTextFieldStyle())
                        .padding(.leading, 4)
                    
                }
                .padding(.horizontal, 10)
                .frame(width: 350, height: 40)
                .background(AppColor.darkGrayBackground.color)
                .cornerRadius(20)
            }
            .padding(.bottom, 16)
            if videoAnalyticsViewModel.isLoading {
                ForEach(0..<5){ _ in
                    VideoSkeletonView()
                        .padding()
                        .blinking(duration: 0.75)
                }
                
            } else if let error = videoAnalyticsViewModel.errorMessage {
                Text(error)
                    .foregroundColor(AppColor.youtubeRed.color)
                    .padding()
            } else if filteredVideos.isEmpty {
                Spacer()
                HStack {
                    Spacer()
                    EmptyVideosCardView(
                        title: TextConstants.UserVideos.noVideosBySearchTitle,
                        description: TextConstants.UserVideos.noVideosBySearchDesc
                    )
                    Spacer()
                }
                Spacer()
            } else if let videos = videoAnalyticsViewModel.allUploadedVideos, videos.isEmpty {
                Spacer()
                HStack {
                    Spacer()
                    EmptyVideosCardView(
                        title: TextConstants.UserVideos.noVideosTitle,
                        description: TextConstants.UserVideos.noVideosDescription
                    )
                    Spacer()
                }
                Spacer()
            }



            
            // Video List
            ScrollView {
                LazyVStack(spacing: 16) {
                    ForEach(filteredVideos) { video in
                        VideoItemView(video: video,isVideos:true)
                    }
                    
                }
                .padding(.vertical, 8)
            }
            
            if searchText.isEmpty, let _ = videoAnalyticsViewModel.nextPageToken, !videoAnalyticsViewModel.isLoading {
                Button(action: {
                    Task {
                        await videoAnalyticsViewModel.fetchNextVideos()
                    }
                }) {
                    HStack {
                        Text("Load more")
                            .font(AppFontStyle.callout.style.bold())
                            .foregroundStyle(AppColor.youtubeRed.color)
                        Image(systemName: "arrow.down.circle.fill")
                            .foregroundStyle(AppColor.youtubeRed.color)
                    }
                    
                    .padding(.horizontal, 16)
                    .padding(.vertical)
                    .clipShape(RoundedRectangle(cornerRadius: 10))
                    .shadow(color: AppColor.youtubeRed.color.opacity(0.3), radius: 6, x: 0, y: 2)
                }
                .buttonStyle(PlainButtonStyle())
                .frame(maxWidth: .infinity, alignment: .center)
                
                
                
            }
            
        }
        .task {
            await videoAnalyticsViewModel.fetchUploadsAndVideos()        }
    }
}


