//
//  VideoSummarizationDetailView.swift
//  YoutubeUploaderVer1
//
//  Created by <PERSON><PERSON><PERSON>  on 26/05/25.
//

import SwiftUI

struct AIDetailView: View {
    let option: AIOption
    let transcriptItems: [(TimeInterval, TimeInterval, String)]
    let selectedFileURL: URL?
    let videoTitle: String
    let videoDescription: String
    let videoCategory: String
    @State private var showCopyAlert = false
    @StateObject private var viewModel = VideoSummaryViewModel()
    
    var body: some View {
        VStack(alignment: .leading, spacing: 20) {
//            Text(option.title)
//                .font(AppFontStyle.title2.style.weight(.bold))
//                .foregroundColor(AppColor.primary.color)
            
            // Example content based on the AI option selected
            switch option {
            case .videoSummarization:
                VideoSummarizationView(
                    viewModel: viewModel,
                    transcriptItems: transcriptItems
                )


            case .contentFreshness:
                ContentFreshnessView(
                    title: videoTitle,
                    description: videoDescription,
                    transcript: transcriptItems,
                    tags: [],
                    category: videoCategory
                )

            case .performancePredictor:
                PerformancePredictorView(
                    title: videoTitle,
                    description: videoDescription,
                    category: videoCategory,
                    thumbnailDescription: ""
                )

            case .shortsClips, .contentRecreation:
                ShortsClipsCreationView(
                    videoURL: selectedFileURL,
                    viewModel: viewModel,
                    transcriptItems: transcriptItems
                )
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(AppColor.darkGrayBackground.color)
        )
        .transition(.opacity.combined(with: .move(edge: .top)))
    }
    
}


