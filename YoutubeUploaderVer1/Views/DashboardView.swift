

//
//  DashboardView.swift
//  YoutubeUploaderVer1
//
//  Created by <PERSON><PERSON><PERSON>  on 06/04/25.
//

import SwiftUI
import GoogleSignIn

struct DashboardView: View {
    let signedInUser: GIDGoogleUser
    @StateObject private var coordinator = Coordinator()
    @EnvironmentObject private var navigationCoordinator: NavigationCoordinator
    @State private var selectedTab: CreatorTab = .overview
    @State private var isModalPresented = false
    let localAIService = LocalAIService.shared
    @EnvironmentObject var sharedVideoHandler: SharedVideoHandler
   
    
    
    var body: some View {
        ZStack {
            // Modern gradient background
            LinearGradient(
                colors: [
                    AppColor.backgroundGradientStart.color,
                    AppColor.backgroundGradientEnd.color
                ],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            .ignoresSafeArea()

            VStack(spacing: 0) {
                // Top navigation with modern styling
                YTCreatorTopNavBar(isModalPresented: $isModalPresented, selectedTab: $coordinator.selectedTab)

                HStack(alignment: .top, spacing: 0) {
                    // Sidebar with modern styling
                    YTCreatorSideNavBar(selectedTab: $coordinator.selectedTab)

                    // Main content area
                    NavigationStack(path: $navigationCoordinator.path) {
                        ScrollView {
                            VStack(alignment: .leading) {
                                coordinator.viewForSelectedTab(using: navigationCoordinator)
                            }
                            .frame(maxWidth: .infinity, alignment: .leading)
                        }
                        .navigationDestination(for: NavigationRoute.self) { route in
                            switch route {
                            case .playlistDetailView:
                                PlaylistDetailView()
                                    .padding(24)
                                    .navigationBarBackButtonHidden(true)
                                    .background(AppColor.surfacePrimary.color)

                            case .videoAnalyticsView(let video):
                                VideoAnalyticsGraphView(video: video, isVideos: true)
                                    .padding(24)
                                    .navigationBarBackButtonHidden(true)
                                    .background(AppColor.surfacePrimary.color)

                            case .playlistVideoAnalyticsView(let video):
                                VideoAnalyticsGraphView(video: video, isVideos: false)
                                    .padding(24)
                                    .navigationBarBackButtonHidden(true)
                                    .background(AppColor.surfacePrimary.color)

                            case .videoUploadView(let videoUrl):
                                VideosUploadView(videoURL: videoUrl)
                                    .padding(.horizontal, 24)
                                    .navigationBarBackButtonHidden(true)
                                    .background(AppColor.surfacePrimary.color)
                            }
                        }
                    }
                    .background(Color.clear)
                }
                .frame(maxHeight: .infinity)
            }
        }
        .ignoresSafeArea(.container, edges: .top)
        .onChange(of: coordinator.selectedTab) { _ in
            navigationCoordinator.navigateToNewTab()
        }
//        .onAppear {
//            sharedVideoHandler.checkForSharedVideo()
//        }
    }
    
}
