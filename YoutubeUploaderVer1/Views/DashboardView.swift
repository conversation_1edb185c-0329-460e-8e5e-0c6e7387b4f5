

//
//  DashboardView.swift
//  YoutubeUploaderVer1
//
//  Created by <PERSON><PERSON><PERSON>  on 06/04/25.
//

import SwiftUI
import GoogleSignIn

struct DashboardView: View {
    let signedInUser: GIDGoogleUser
    @StateObject private var coordinator = Coordinator()
    @EnvironmentObject private var navigationCoordinator: NavigationCoordinator
    @State private var selectedTab: CreatorTab = .overview
    @State private var isModalPresented = false
    @AppStorage("isSidebarCollapsed") private var isSidebarCollapsed = false
    let localAIService = LocalAIService.shared
    @EnvironmentObject var sharedVideoHandler: SharedVideoHandler
    @StateObject private var processMonitor = ProcessMonitoringService.shared
    @State private var pendingTabChange: CreatorTab? = nil
    @State private var showProcessAlert = false
   
    
    
    var body: some View {
        ZStack {
            // Modern gradient background
            LinearGradient(
                colors: [
                    AppColor.backgroundGradientStart.color,
                    AppColor.backgroundGradientEnd.color
                ],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            .ignoresSafeArea()

            VStack(spacing: 0) {
                // Top navigation with modern styling
                YTCreatorTopNavBar(isModalPresented: $isModalPresented, selectedTab: $coordinator.selectedTab)

                HStack(alignment: .top, spacing: 0) {
                    // Sidebar with modern styling
                    YTCreatorSideNavBar(
                        selectedTab: $coordinator.selectedTab,
                        isCollapsed: $isSidebarCollapsed
                    )

                    // Main content area
                    NavigationStack(path: $navigationCoordinator.path) {
                        ScrollView {
                            VStack(alignment: .leading) {
                                coordinator.viewForSelectedTab(using: navigationCoordinator)
                            }
                            .frame(maxWidth: .infinity, alignment: .leading)
                        }
                        .navigationDestination(for: NavigationRoute.self) { route in
                            switch route {
                            case .playlistDetailView:
                                PlaylistDetailView()
                                    .padding(24)
                                    .navigationBarBackButtonHidden(true)
                                    .background(AppColor.surfacePrimary.color)

                            case .videoAnalyticsView(let video):
                                VideoAnalyticsGraphView(video: video, isVideos: true)
                                    .padding(24)
                                    .navigationBarBackButtonHidden(true)
                                    .background(AppColor.surfacePrimary.color)

                            case .playlistVideoAnalyticsView(let video):
                                VideoAnalyticsGraphView(video: video, isVideos: false)
                                    .padding(24)
                                    .navigationBarBackButtonHidden(true)
                                    .background(AppColor.surfacePrimary.color)

                            case .videoUploadView(let videoUrl):
                                VideosUploadView(videoURL: videoUrl)
                                    .padding(.horizontal, 24)
                                    .navigationBarBackButtonHidden(true)
                                    .background(AppColor.surfacePrimary.color)
                            }
                        }
                    }
                    .background(Color.clear)
                }
                .frame(maxHeight: .infinity)
            }
        }
        .ignoresSafeArea(.container, edges: .top)
        .onChange(of: coordinator.selectedTab) { newTab in
            handleTabChange(to: newTab)
        }
        .onReceive(NotificationCenter.default.publisher(for: NSNotification.Name("ToggleSidebar"))) { _ in
            withAnimation(.easeInOut(duration: 0.3)) {
                isSidebarCollapsed.toggle()
            }
        }
        .alert("⚠️ Active Processes", isPresented: $showProcessAlert) {
            Button("Cancel Processes & Change Tab", role: .destructive) {
                processMonitor.confirmTabChange(to: pendingTabChange ?? .overview)
                if let newTab = pendingTabChange {
                    coordinator.selectedTab = newTab
                    navigationCoordinator.navigateToNewTab()
                }
                pendingTabChange = nil
            }
            Button("Stay Here", role: .cancel) {
                processMonitor.cancelTabChange()
                // Revert the tab selection
                if let currentTab = pendingTabChange {
                    coordinator.selectedTab = .uploadVideos
                }
                pendingTabChange = nil
            }
        } message: {
            Text("The following processes are currently running:\n\n• \(processMonitor.activeProcessDescriptions.joined(separator: "\n• "))\n\nChanging tabs will cancel these processes and you may lose your progress. Do you want to continue?")
        }
//        .onAppear {
//            sharedVideoHandler.checkForSharedVideo()
//        }
    }

    // MARK: - Tab Change Handling
    private func handleTabChange(to newTab: CreatorTab) {
        processMonitor.requestTabChange(to: newTab) { allowed in
            if allowed {
                // No active processes or not on upload view - allow immediate change
                navigationCoordinator.navigateToNewTab()
            } else {
                // Has active processes - show confirmation
                pendingTabChange = newTab
                showProcessAlert = true
            }
        }
    }
}
