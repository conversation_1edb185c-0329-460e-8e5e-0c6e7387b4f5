//
//  ProcessMonitoringService.swift
//  YoutubeUploaderVer1
//
//  Created by AI Assistant on 26/04/25.
//

import Foundation
import SwiftUI

// MARK: - Process Monitoring Service
class ProcessMonitoringService: ObservableObject {
    static let shared = ProcessMonitoringService()
    
    @Published var activeProcesses: Set<ProcessType> = []
    @Published var isMonitoringEnabled: Bool = false
    @Published var currentView: CreatorTab = .overview
    
    private var processCheckCallback: (() -> Bool)? = nil
    private var tabChangeCallback: ((CreatorTab) -> Void)? = nil
    
    private init() {}
    
    // MARK: - Process Management
    func addProcess(_ process: ProcessType) {
        activeProcesses.insert(process)
    }
    
    func removeProcess(_ process: ProcessType) {
        activeProcesses.remove(process)
    }
    
    func clearAllProcesses() {
        activeProcesses.removeAll()
    }
    
    var hasActiveProcesses: Bool {
        !activeProcesses.isEmpty
    }
    
    var activeProcessDescriptions: [String] {
        activeProcesses.map { $0.description }
    }
    
    // MARK: - Tab Change Monitoring
    func enableMonitoring(for view: CreatorTab, processCheck: @escaping () -> Bool) {
        currentView = view
        isMonitoringEnabled = true
        processCheckCallback = processCheck
    }
    
    func disableMonitoring() {
        isMonitoringEnabled = false
        processCheckCallback = nil
        tabChangeCallback = nil
        clearAllProcesses()
    }
    
    func setTabChangeCallback(_ callback: @escaping (CreatorTab) -> Void) {
        tabChangeCallback = callback
    }
    
    // MARK: - Tab Change Interception
    func requestTabChange(to newTab: CreatorTab, completion: @escaping (Bool) -> Void) {
        // Only intercept if monitoring is enabled and we're leaving the monitored view
        guard isMonitoringEnabled, 
              currentView == .uploadVideos,
              newTab != .uploadVideos else {
            completion(true)
            return
        }
        
        // Check for active processes
        if let processCheck = processCheckCallback, processCheck() {
            // Has active processes - need user confirmation
            completion(false)
        } else {
            // No active processes - allow tab change
            completion(true)
        }
    }
    
    func confirmTabChange(to newTab: CreatorTab) {
        disableMonitoring()
        tabChangeCallback?(newTab)
    }
    
    func cancelTabChange() {
        // Do nothing - stay on current tab
    }
}

// MARK: - Process Type Extension
extension ProcessType {
    var priority: Int {
        switch self {
        case .videoUpload: return 4
        case .transcriptGeneration: return 3
        case .audioConversion: return 2
        case .aiEnhancement: return 1
        }
    }
}
